# VS Code Setup to Replicate Visual Studio 2022 Light Theme UI/UX

This guide provides step-by-step instructions to configure VS Code to match Visual Studio 2022's default light theme appearance, behavior, and development experience.

## Step 1: Required Extensions

Install these extensions to get the closest Visual Studio 2022 light theme experience:

### Core Theme Extensions
1. **Visual Studio Theme** - Microsoft's official VS theme
   - Extension ID: `ms-vscode.vscode-theme-visualstudio`
   - Provides authentic Visual Studio Light theme (default VS2022 theme)

2. **Visual Studio Icons** - Official VS icon theme
   - Extension ID: `ms-vscode.vs-icons-visual-studio`
   - Provides the same file icons as Visual Studio 2022

### C# Development Extensions
3. **C# Dev Kit** - Microsoft's official C# extension
   - Extension ID: `ms-dotnettools.csdevkit`
   - Provides IntelliSense, debugging, and project management

4. **OmniSharp for C#** - Enhanced C# support
   - Extension ID: `ms-dotnettools.csharp`
   - Advanced C# language features

### UI Enhancement Extensions
5. **Error Lens** - Inline error display (like VS2022)
   - Extension ID: `usernamehw.errorlens`
   - Shows errors directly in code editor

6. **Bracket Pair Colorizer 2** - Colored bracket matching
   - Extension ID: `coenraads.bracket-pair-colorizer-2`
   - Visual bracket matching like VS2022

7. **Auto Rename Tag** - Synchronized tag editing
   - Extension ID: `formulahendry.auto-rename-tag`
   - For XAML/XML development

## Step 2: Font Installation

Install the **Cascadia Code** font family (VS2022's default):

1. Download from: https://github.com/microsoft/cascadia-code/releases
2. Install all font variants (Regular, Bold, Italic, etc.)
3. Restart VS Code after installation

## Step 3: Apply Settings

1. Open VS Code Settings (Ctrl+,)
2. Click the "Open Settings (JSON)" icon in the top right
3. Copy the contents from `vscode-vs2022-settings.json` into your settings file
4. Save and restart VS Code

## Step 4: Keyboard Shortcuts (Optional)

To match Visual Studio 2022 keyboard shortcuts:

1. Open Command Palette (Ctrl+Shift+P)
2. Type "Preferences: Open Keyboard Shortcuts (JSON)"
3. Add Visual Studio keymap extension or manually configure shortcuts

### Key VS2022 Shortcuts to Configure:
- **F5**: Start Debugging
- **Ctrl+F5**: Start Without Debugging
- **F9**: Toggle Breakpoint
- **F10**: Step Over
- **F11**: Step Into
- **Shift+F11**: Step Out
- **Ctrl+Shift+F**: Find in Files
- **Ctrl+K, Ctrl+C**: Comment Selection
- **Ctrl+K, Ctrl+U**: Uncomment Selection

## Step 5: Workspace Layout

Configure the workspace to match VS2022:

1. **Solution Explorer**: Use the Explorer sidebar (Ctrl+Shift+E)
2. **Output Window**: Open Terminal panel (Ctrl+`)
3. **Error List**: Use Problems panel (Ctrl+Shift+M)
4. **Properties**: Use outline view in Explorer

## Step 6: C# Project Configuration

For optimal C# development experience:

1. Create or open a `.csproj` file
2. Ensure these project properties are set:
   ```xml
   <PropertyGroup>
     <TargetFramework>net8.0</TargetFramework>
     <UseWPF>true</UseWPF>
     <Nullable>enable</Nullable>
     <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
   </PropertyGroup>
   ```

## Step 7: IntelliSense Configuration

The settings file configures IntelliSense to match VS2022:

- Auto-completion on typing
- Parameter hints
- Quick info on hover
- Import suggestions
- Inline type hints
- Error squiggles and inline error messages

## Step 8: Debugging Setup

Configure debugging to match VS2022:

1. Create `.vscode/launch.json` in your project root
2. Configure for .NET applications:
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": ".NET Core Launch (WPF)",
         "type": "coreclr",
         "request": "launch",
         "program": "${workspaceFolder}/bin/Debug/net8.0/YourApp.dll",
         "args": [],
         "cwd": "${workspaceFolder}",
         "console": "internalConsole",
         "stopAtEntry": false
       }
     ]
   }
   ```

## Key Features Configured

### Visual Appearance
- **Light Theme**: Matches VS2022's default light theme exactly
- **Fonts**: Cascadia Code with proper sizing and ligatures
- **Colors**: Authentic VS2022 light color scheme for all UI elements
- **Icons**: Official Visual Studio file and folder icons

### Editor Behavior
- **Line Numbers**: Always visible with VS2022-style blue colors
- **Minimap**: Enabled on the right side
- **Bracket Matching**: Visual bracket highlighting
- **Indentation**: 4 spaces for C#, proper tab handling
- **Text Wrapping**: Configured for optimal code viewing

### Development Features
- **IntelliSense**: Full C# language support
- **Error Display**: Inline errors with Error Lens
- **Code Formatting**: Automatic formatting on save
- **Git Integration**: Built-in source control
- **Debugging**: Full debugging support with breakpoints

### Layout and Navigation
- **Sidebar**: Solution Explorer equivalent with light theme colors
- **Status Bar**: VS2022-style blue status information
- **Breadcrumbs**: File and symbol navigation
- **Tabs**: VS2022-style editor tabs with light theme colors

## Verification

After applying all settings, you should see:

1. Light theme matching VS2022 exactly
2. Cascadia Code font in editor and terminal
3. Inline error display with Error Lens
4. VS2022-style file icons
5. Familiar keyboard shortcuts and behavior
6. Proper C# IntelliSense and debugging

## Troubleshooting

### Theme Not Applied
- Ensure Visual Studio Theme extension is installed
- Restart VS Code after installing extensions
- Check that "Visual Studio Dark" is selected in Color Theme

### Font Issues
- Verify Cascadia Code is installed system-wide
- Restart VS Code after font installation
- Check font family setting in VS Code settings

### IntelliSense Problems
- Ensure C# Dev Kit extension is installed and enabled
- Open a .csproj file to activate C# language server
- Check that .NET SDK is installed and configured

### Performance Issues
- Disable unnecessary extensions
- Increase VS Code memory limit if needed
- Use file exclusion patterns in settings

## Additional Resources

- [VS Code C# Documentation](https://code.visualstudio.com/docs/languages/csharp)
- [Visual Studio Theme Extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-theme-visualstudio)
- [Cascadia Code Font](https://github.com/microsoft/cascadia-code)
- [VS Code Debugging Guide](https://code.visualstudio.com/docs/editor/debugging)

This configuration will give you a VS Code experience that closely matches Visual Studio 2022's look, feel, and functionality for C# WPF development.
