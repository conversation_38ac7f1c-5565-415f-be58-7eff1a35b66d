using System.Security.Principal;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Interface for administrator privilege checking and validation services.
    /// Provides methods to validate elevated permissions required for system optimization operations.
    /// Essential for ensuring application has necessary permissions before performing system modifications.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: admin privileges, UAC, administrator check, privilege validation, elevated permissions
    /// </remarks>
    public interface IAdminPrivilegeService
    {
        /// <summary>
        /// Gets whether the application is currently running with administrator privileges.
        /// Used throughout the application to conditionally enable/disable features based on permissions.
        /// </summary>
        bool IsRunningAsAdministrator { get; }
        
        /// <summary>
        /// Gets whether the application can perform administrative operations.
        /// Currently equivalent to IsRunningAsAdministrator but allows for future expansion.
        /// </summary>
        bool CanPerformAdminOperations { get; }
        
        /// <summary>
        /// Retrieves a human-readable message describing the current privilege status.
        /// Used for displaying privilege information to users in the UI.
        /// </summary>
        /// <returns>Descriptive message about current privilege level and available functionality</returns>
        string GetPrivilegeStatusMessage();
        
        /// <summary>
        /// Checks if the current privilege level is sufficient for a specific operation.
        /// Logs warnings when operations require elevated privileges that are not available.
        /// </summary>
        /// <param name="operationName">Name of the operation requiring privilege validation</param>
        /// <returns>True if operation can be performed with current privileges, false otherwise</returns>
        bool CheckPrivilegeForOperation(string operationName);
    }

    /// <summary>
    /// Service implementation for administrator privilege checking and management.
    /// Provides real-time privilege validation and status reporting for system optimization operations.
    /// Uses Windows Identity and Principal classes to determine UAC elevation status.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: admin privilege service, UAC validation, Windows identity, privilege checking
    /// Critical service for system security and operation safety validation
    /// </remarks>
    public class AdminPrivilegeService : IAdminPrivilegeService
    {
        /// <summary>
        /// Cached result of administrator privilege check performed during service initialization.
        /// Provides fast access to privilege status without repeated system calls.
        /// </summary>
        public bool IsRunningAsAdministrator { get; private set; }
        
        /// <summary>
        /// Indicates whether administrative operations can be performed.
        /// Currently equivalent to IsRunningAsAdministrator but designed for future extensibility.
        /// </summary>
        public bool CanPerformAdminOperations => IsRunningAsAdministrator;

        /// <summary>
        /// Initializes the AdminPrivilegeService and performs initial privilege validation.
        /// Automatically checks administrator status during construction for immediate availability.
        /// </summary>
        public AdminPrivilegeService()
        {
            // Perform immediate privilege check during service initialization
            CheckAdminPrivileges();
        }

        /// <summary>
        /// Performs Windows identity and principal validation to determine administrator privileges.
        /// Uses Windows Security API to check if current user is member of Administrators group.
        /// Handles exceptions gracefully to ensure service remains functional even if privilege check fails.
        /// </summary>
        /// <remarks>
        /// AI Search Keywords: Windows identity check, principal validation, administrator role check
        /// </remarks>
        private void CheckAdminPrivileges()
        {
            try
            {
                // Get current Windows user identity
                var identity = WindowsIdentity.GetCurrent();
                
                // Create principal to check role membership
                var principal = new WindowsPrincipal(identity);
                
                // Check if user is member of built-in Administrators role
                IsRunningAsAdministrator = principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                // Default to false if any exception occurs during privilege checking
                // This ensures the application remains functional but assumes no admin privileges
                IsRunningAsAdministrator = false;
            }
        }

        /// <summary>
        /// Provides user-friendly status message indicating current privilege level and functionality availability.
        /// Returns descriptive text explaining what operations are available based on current permissions.
        /// Used in UI components to inform users about privilege requirements and limitations.
        /// </summary>
        /// <returns>
        /// Descriptive message about administrator privileges and available functionality.
        /// Positive message if elevated, informative warning if not elevated.
        /// </returns>
        /// <remarks>
        /// AI Search Keywords: privilege status message, user feedback, privilege display
        /// </remarks>
        public string GetPrivilegeStatusMessage()
        {
            return IsRunningAsAdministrator 
                ? "Running with Administrator privileges - All features available"
                : "Running without Administrator privileges - Limited functionality";
        }

        /// <summary>
        /// Validates whether current privilege level is sufficient for a specific operation.
        /// Provides operation-specific privilege checking with automatic logging of privilege violations.
        /// Used by services to validate permissions before attempting privileged operations.
        /// </summary>
        /// <param name="operationName">
        /// Name of the operation requiring privilege validation.
        /// Used for logging and error reporting purposes.
        /// </param>
        /// <returns>
        /// True if the operation can be performed with current privileges (admin mode).
        /// False if the operation requires elevation that is not available.
        /// </returns>
        /// <remarks>
        /// AI Search Keywords: operation privilege check, privilege validation, operation security
        /// Automatically logs warnings when operations are blocked due to insufficient privileges
        /// </remarks>
        public bool CheckPrivilegeForOperation(string operationName)
        {
            if (IsRunningAsAdministrator)
                return true;

            // Log warning about privilege requirement for audit and debugging purposes
            Serilog.Log.Warning("Operation '{OperationName}' requires administrator privileges but application is not elevated", operationName);
            return false;
        }
    }
}
