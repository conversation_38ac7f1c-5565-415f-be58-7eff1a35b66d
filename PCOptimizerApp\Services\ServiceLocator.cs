using Microsoft.Extensions.DependencyInjection;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Static service locator providing access to dependency injection services from non-DI contexts.
    /// Implements the Service Locator pattern as a bridge between dependency injection container 
    /// and legacy code or scenarios where constructor injection is not available (like WPF Pages).
    /// 
    /// WARNING: Use sparingly! Prefer constructor injection whenever possible for better testability
    /// and explicit dependency declaration. Only use in scenarios where DI is not feasible.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: service locator, dependency injection bridge, service resolution, legacy DI support
    /// Used primarily for WPF Pages and other contexts where constructor injection is not available
    /// </remarks>
    public static class ServiceLocator
    {
        /// <summary>
        /// The dependency injection service provider instance used for service resolution.
        /// Initialized once during application startup and used throughout application lifetime.
        /// </summary>
        private static IServiceProvider? _serviceProvider;

        /// <summary>
        /// Initializes the service locator with the dependency injection service provider.
        /// Must be called once during application startup before any services can be resolved.
        /// Typically called from App.xaml.cs after building the host container.
        /// </summary>
        /// <param name="serviceProvider">
        /// The service provider from the DI container containing all registered services.
        /// Usually obtained from IHost.Services after building the dependency injection container.
        /// </param>
        /// <remarks>
        /// AI Search Keywords: service locator initialization, DI container setup, service provider initialization
        /// </remarks>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Retrieves a required service of the specified type from the dependency injection container.
        /// Throws an exception if the service is not registered or if the ServiceLocator is not initialized.
        /// Use this method when the service is expected to exist and failure should stop execution.
        /// </summary>
        /// <typeparam name="T">
        /// The type of service to retrieve. Must be registered in the DI container.
        /// Should be an interface type for best practices (e.g., ISystemInfoService).
        /// </typeparam>
        /// <returns>
        /// The service instance from the DI container.
        /// Never returns null - throws exception if service cannot be resolved.
        /// </returns>
        /// <exception cref="InvalidOperationException">
        /// Thrown when ServiceLocator is not initialized via Initialize() method.
        /// </exception>
        /// <exception cref="InvalidOperationException">
        /// Thrown when the requested service type is not registered in the DI container.
        /// </exception>
        /// <remarks>
        /// AI Search Keywords: required service resolution, service retrieval, dependency resolution
        /// </remarks>
        public static T GetService<T>() where T : notnull
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("ServiceLocator is not initialized. Call Initialize() first.");
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Retrieves an optional service of the specified type from the dependency injection container.
        /// Returns null if the service is not registered or if the ServiceLocator is not initialized.
        /// Use this method when the service is optional and the application should continue if not available.
        /// </summary>
        /// <typeparam name="T">
        /// The type of service to retrieve. Must be a reference type.
        /// Should be an interface type for best practices (e.g., IOptionalService).
        /// </typeparam>
        /// <returns>
        /// The service instance from the DI container if available and ServiceLocator is initialized.
        /// Returns null if service is not registered or ServiceLocator is not initialized.
        /// </returns>
        /// <remarks>
        /// AI Search Keywords: optional service resolution, nullable service retrieval, safe service access
        /// </remarks>
        public static T? GetServiceOrNull<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                return null;
            }

            return _serviceProvider.GetService<T>();
        }

        /// <summary>
        /// Gets whether the ServiceLocator has been initialized with a service provider.
        /// Used to check initialization status before attempting service resolution.
        /// Useful for defensive programming and validation in critical code paths.
        /// </summary>
        /// <returns>
        /// True if Initialize() has been called with a valid service provider.
        /// False if ServiceLocator has not been initialized yet.
        /// </returns>
        /// <remarks>
        /// AI Search Keywords: initialization check, service locator status, initialization validation
        /// </remarks>
        public static bool IsInitialized => _serviceProvider != null;
    }
}
