<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AssemblyTitle>PC Optimizer Pro</AssemblyTitle>
    <AssemblyDescription>Advanced Windows PC Optimization Tool</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>PC Optimizer Pro</Company>
    <Product>PC Optimizer Pro</Product>
    <Copyright>Copyright © 2024</Copyright>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
    <RunAnalyzersDuringLiveAnalysis>false</RunAnalyzersDuringLiveAnalysis>
    <EnableNETAnalyzers>false</EnableNETAnalyzers>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Windows.CsWin32" Version="0.3.106" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\**\*" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Assets\Icons\" />
    <Folder Include="Assets\Images\" />
    <Folder Include="Models\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
    <Folder Include="Commands\" />
    <Folder Include="Converters\" />
  </ItemGroup>
</Project>