using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using PCOptimizerApp.Controls;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using PCOptimizerApp.ViewModels;
using Serilog;

namespace PCOptimizerApp.Views
{
    /// <summary>
    /// Modern optimization page with category-based optimization management
    /// </summary>
    public partial class ModernOptimizationPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<ModernOptimizationPage>();
        private readonly IOptimizationService _optimizationService;
        private readonly IAdminPrivilegeService _adminPrivilegeService;
        private readonly IAIExplanationService _aiExplanationService;
        private readonly ISystemAnalysisService _systemAnalysisService;

        private List<OptimizationItemViewModel> _allOptimizations = new();
        private List<OptimizationItemViewModel> _currentOptimizations = new();
        private string _selectedCategory = string.Empty;
        private bool _isOptimizationInProgress = false;

        private readonly Dictionary<string, string> _simpleExplanations = new()
        {
            { "preparing", "Getting ready to optimize your PC. We're checking what needs to be improved." },
            { "backup", "Creating a backup of your current settings so we can restore them if needed." },
            { "visual_effects", "Adjusting visual effects to make your computer run faster while keeping it looking good." },
            { "cpu_optimization", "Optimizing your processor settings to get the best performance from your CPU." },
            { "memory_management", "Improving how your computer uses memory to run programs more efficiently." },
            { "startup_programs", "Disabling unnecessary programs that slow down your computer's startup." },
            { "registry_cleanup", "Cleaning up the Windows registry to remove outdated and broken entries." },
            { "disk_cleanup", "Removing temporary files and clearing caches to free up storage space." },
            { "ssd_optimization", "Optimizing your SSD drive for better speed and longer lifespan." },
            { "power_management", "Adjusting power settings to balance performance and energy efficiency." },
            { "gaming_mode", "Enabling special settings to give you the best gaming experience." },
            { "telemetry", "Reducing data collection by Windows to improve privacy and performance." },
            { "browser_optimization", "Optimizing browser settings for faster scrolling, rendering, and tab switching." },
            { "finalizing", "Finishing up the optimization and making sure everything is working correctly." }
        };

        private readonly Dictionary<string, string> _technicalExplanations = new()
        {
            { "preparing", "Initializing optimization engine, checking system configuration, and validating optimization targets." },
            { "backup", "Creating registry backup in %TEMP%\\PCOptimizer\\Backups\\ and saving current system state." },
            { "visual_effects", "Modifying HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects registry entries." },
            { "cpu_optimization", "Updating processor scheduling, priority class assignments, and CPU affinity settings." },
            { "memory_management", "Configuring virtual memory paging, working set limits, and memory management policies." },
            { "startup_programs", "Modifying HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run registry keys." },
            { "registry_cleanup", "Scanning and removing orphaned registry entries, invalid file associations, and broken COM references." },
            { "disk_cleanup", "Executing disk cleanup operations: temp files, browser cache, system logs, and prefetch data." },
            { "ssd_optimization", "Disabling defragmentation, enabling TRIM, configuring write caching policies for solid-state drives." },
            { "power_management", "Modifying power scheme GUIDs, processor power states, and device power management settings." },
            { "gaming_mode", "Enabling Game Mode, disabling Game DVR, optimizing GPU scheduling and thread priorities." },
            { "telemetry", "Modifying telemetry registry keys: DiagTrack, dmwappushservice, and Windows Customer Experience." },
            { "browser_optimization", "Configuring browser registry settings: smooth scrolling, hardware acceleration, tab sleeping, and background processes." },
            { "finalizing", "Validating applied changes, updating optimization status cache, and performing system integrity checks." }
        };

        private readonly List<string> _registryChanges = new();
        private System.Threading.CancellationTokenSource? _cancellationTokenSource;
        private SystemAnalysisContext? _systemContext;

        public ModernOptimizationPage(
            IOptimizationService optimizationService,
            IAdminPrivilegeService adminPrivilegeService,
            IAIExplanationService aiExplanationService,
            ISystemAnalysisService systemAnalysisService)
        {
            InitializeComponent();
            _optimizationService = optimizationService;
            _adminPrivilegeService = adminPrivilegeService;
            _aiExplanationService = aiExplanationService;
            _systemAnalysisService = systemAnalysisService;

            Loaded += ModernOptimizationPage_Loaded;
        }

        private void ModernOptimizationPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Information("ModernOptimizationPage_Loaded started");

                // Always populate basic tree view immediately to ensure something is shown
                _logger.Information("Populating basic tree view immediately");
                LoadOptimizationsAndPopulateTreeView();

                _logger.Information("Optimization loading initiated successfully");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error loading optimization page");
                ShowError($"Failed to load optimizations: {ex.Message}");
            }
        }

        private void LoadOptimizationsAndPopulateTreeView()
        {
            try
            {
                _logger.Information("Loading ALL optimizations and populating tree view");

                // Show a loading indicator immediately
                Dispatcher.Invoke(() =>
                {
                    try
                    {
                        var treeView = FindName("CategoryTreeView") as TreeView;
                        if (treeView != null)
                        {
                            treeView.Items.Clear();

                            // Add a loading indicator
                            var loadingItem = new TreeViewItem
                            {
                                Header = new TextBlock
                                {
                                    Text = "⏳ Loading optimizations...",
                                    FontStyle = FontStyles.Italic,
                                    Foreground = Brushes.Gray
                                },
                                IsEnabled = false
                            };
                            treeView.Items.Add(loadingItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error showing loading indicator");
                    }
                });

                // Load optimizations in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        _logger.Information("Loading all optimizations including software-specific ones");
                        var allOptimizations = await _optimizationService.GetAllOptimizationsAsync();

                        // Convert to view models
                        var viewModels = allOptimizations
                            .Select(o => new OptimizationItemViewModel
                            {
                                Id = o.Id ?? string.Empty,
                                Name = o.Name ?? string.Empty,
                                Description = o.Description ?? string.Empty,
                                Category = o.Category ?? string.Empty,
                                IsApplicable = o.IsApplicable,
                                IsApplied = o.IsApplied,
                                IsSelected = true, // All selected by default
                                Safety = o.Safety,
                                Impact = o.Impact,
                                IsReversible = o.IsReversible,
                                ExpectedImprovement = o.ExpectedImprovement ?? string.Empty
                            })
                            .ToList();

                        // Update UI on main thread
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                _allOptimizations = viewModels;
                                _logger.Information("Loaded {Count} total optimizations", _allOptimizations.Count);
                                PopulateTreeViewFromOptimizations();
                                _logger.Information("Tree view populated with all optimizations");
                            }
                            catch (Exception ex)
                            {
                                _logger.Error(ex, "Error updating UI with all optimizations");
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error loading all optimizations, falling back to basic optimizations");

                        // Fallback to basic optimizations if there's an error
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                var basicOptimizations = GetFallbackOptimizations();
                                _allOptimizations = basicOptimizations
                                    .Select(o => new OptimizationItemViewModel
                                    {
                                        Id = o.Id ?? string.Empty,
                                        Name = o.Name ?? string.Empty,
                                        Description = o.Description ?? string.Empty,
                                        Category = o.Category ?? string.Empty,
                                        IsApplicable = o.IsApplicable,
                                        IsApplied = o.IsApplied,
                                        IsSelected = true,
                                        Safety = o.Safety,
                                        Impact = o.Impact,
                                        IsReversible = o.IsReversible,
                                        ExpectedImprovement = o.ExpectedImprovement ?? string.Empty
                                    })
                                    .ToList();
                                PopulateTreeViewFromOptimizations();
                            }
                            catch (Exception fallbackEx)
                            {
                                _logger.Error(fallbackEx, "Even fallback optimization loading failed");
                            }
                        });
                    }
                });

                _logger.Information("Optimization loading initiated successfully");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Critical error in LoadOptimizationsAndPopulateTreeView");
            }
        }


        private static List<OptimizationItem> GetFallbackOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "visual_effects_performance",
                    Name = "Optimize Visual Effects for Performance",
                    Description = "Adjusts Windows visual effects for better performance",
                    Category = "Performance",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% better system responsiveness"
                },
                new OptimizationItem
                {
                    Id = "power_high_performance",
                    Name = "High Performance Power Plan",
                    Description = "Sets Windows power plan to High Performance",
                    Category = "System",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Maximum CPU and system performance"
                },
                new OptimizationItem
                {
                    Id = "chrome_performance_optimize",
                    Name = "Chrome Performance Optimization",
                    Description = "Optimize Chrome scrolling, rendering, and tab switching performance",
                    Category = "Browser",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster web browsing experience"
                },
                new OptimizationItem
                {
                    Id = "edge_performance_optimize",
                    Name = "Edge Performance Optimization",
                    Description = "Optimize Edge scrolling, rendering, and tab switching performance",
                    Category = "Browser",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Enhanced browsing experience"
                },
                new OptimizationItem
                {
                    Id = "ssd_trim_enable",
                    Name = "Enable SSD TRIM",
                    Description = "Enables TRIM command for SSD maintenance",
                    Category = "Storage",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better SSD performance and longevity"
                }
            };
        }

        private void PopulateBasicTreeView()
        {
            try
            {
                _logger.Information("Populating basic tree view as fallback");

                // Ensure we're on the UI thread
                if (!Dispatcher.CheckAccess())
                {
                    Dispatcher.Invoke(PopulateBasicTreeView);
                    return;
                }

                // Clear existing tree view items
                CategoryTreeView.Items.Clear();

                // Create basic categories
                var basicCategories = new[]
                {
                    ("Performance", "🎯 Performance"),
                    ("System", "🔧 System"),
                    ("Storage", "💾 Storage"),
                    ("Hardware", "🖥️ Hardware"),
                    ("Browser", "🌐 Browser"),
                    ("Office", "📝 Office"),
                    ("Gaming", "🎮 Gaming"),
                    ("Development", "💻 Development"),
                    ("Cleanup", "🧹 Cleanup")
                };

                foreach (var (tag, displayName) in basicCategories)
                {
                    var categoryItem = new TreeViewItem
                    {
                        Tag = tag,
                        IsExpanded = true,
                        Header = new TextBlock
                        {
                            Text = displayName,
                            Style = (Style)FindResource("TreeViewHeaderStyle")
                        }
                    };

                    CategoryTreeView.Items.Add(categoryItem);
                }

                _logger.Information("Populated basic tree view with {Count} categories", basicCategories.Length);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error populating basic tree view");
            }
        }

        private void PopulateTreeViewFromOptimizations()
        {
            try
            {
                _logger.Information("Populating tree view from {Count} optimizations", _allOptimizations.Count);

                var treeView = FindName("CategoryTreeView") as TreeView;
                if (treeView == null)
                {
                    _logger.Warning("CategoryTreeView not found, falling back to basic tree view");
                    PopulateBasicTreeView();
                    return;
                }

                treeView.Items.Clear();

                var categoryGroups = _allOptimizations
                    .GroupBy(o => GetMainCategory(o.Category))
                    .OrderBy(g => GetCategoryOrder(g.Key))
                    .ToList();

                foreach (var categoryGroup in categoryGroups)
                {
                    var categoryItem = CreateCategoryTreeViewItem(categoryGroup);
                    treeView.Items.Add(categoryItem);
                }

                SelectAllOptimizations();

                _logger.Information("Successfully populated tree view with {Count} categories and {Total} total optimizations",
                    categoryGroups.Count, _allOptimizations.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error populating tree view from optimizations");
                PopulateBasicTreeView();
            }
        }

        private TreeViewItem CreateCategoryTreeViewItem(IGrouping<string, OptimizationItemViewModel> categoryGroup)
        {
            var mainCategoryItem = new TreeViewItem
            {
                Tag = categoryGroup.Key,
                IsExpanded = true
            };

            var headerText = new TextBlock
            {
                Text = GetCategoryDisplayName(categoryGroup.Key),
                FontWeight = FontWeights.SemiBold,
                FontSize = 13,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 1, 0, 0)
            };

            mainCategoryItem.Header = headerText;

            var optimizationsInCategory = categoryGroup.OrderBy(o => o.Name).ToList();

            foreach (var optimization in optimizationsInCategory)
            {
                var optimizationItem = CreateOptimizationTreeViewItem(optimization);
                mainCategoryItem.Items.Add(optimizationItem);
            }

            return mainCategoryItem;
        }

        private TreeViewItem CreateOptimizationTreeViewItem(OptimizationItemViewModel optimization)
        {
            var optimizationItem = new TreeViewItem
            {
                Tag = optimization.Id,
                Margin = new Thickness(0), // Remove any item margin
                Padding = new Thickness(0), // Remove any item padding
                Style = (Style)FindResource("OptimizationTreeViewItemStyle") // Apply optimization-specific style
            };

            var itemStackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var checkbox = CreateOptimizationCheckbox(optimization);
            var optimizationText = CreateOptimizationText(optimization);

            itemStackPanel.Children.Add(checkbox);
            itemStackPanel.Children.Add(optimizationText);

            if (optimization.Safety != OptimizationSafety.Safe)
            {
                var safetyIcon = CreateSafetyIcon(optimization);
                itemStackPanel.Children.Add(safetyIcon);
            }

            optimizationItem.Header = itemStackPanel;
            return optimizationItem;
        }

        private CheckBox CreateOptimizationCheckbox(OptimizationItemViewModel optimization)
        {
            var checkbox = new CheckBox
            {
                IsChecked = optimization.IsSelected,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 2, 0), // Reduced from 3 to 2
                Tag = optimization.Id
            };

            checkbox.Checked += (s, e) =>
            {
                var opt = _allOptimizations.FirstOrDefault(o => o.Id == optimization.Id);
                if (opt != null) opt.IsSelected = true;
            };
            checkbox.Unchecked += (s, e) =>
            {
                var opt = _allOptimizations.FirstOrDefault(o => o.Id == optimization.Id);
                if (opt != null) opt.IsSelected = false;
            };

            return checkbox;
        }

        private static TextBlock CreateOptimizationText(OptimizationItemViewModel optimization)
        {
            return new TextBlock
            {
                Text = optimization.Name,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 0) // Removed right margin completely
            };
        }

        private static TextBlock CreateSafetyIcon(OptimizationItemViewModel optimization)
        {
            return new TextBlock
            {
                Text = GetSafetyIcon(optimization.Safety),
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 10,
                Margin = new Thickness(2, 0, 0, 0), // Added left margin to separate from text
                ToolTip = $"Safety: {optimization.Safety}"
            };
        }

        private static string GetMainCategory(string category)
        {
            // Map specific categories to main categories
            return category switch
            {
                "Performance" => "Performance",
                "System" => "System",
                "Storage" => "Storage",
                "Hardware" => "Hardware",
                "Browser" => "Browser",
                "Office" => "Office",
                "Gaming" => "Gaming",
                "Development" => "Development",
                "Cleanup" => "Cleanup",
                "Power" => "Power",
                "Startup" => "Startup",
                _ => "Other"
            };
        }

        private async void AutoOptimizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isOptimizationInProgress)
            {
                ShowError("Optimization is already in progress");
                return;
            }

            try
            {
                if (!_adminPrivilegeService.IsRunningAsAdministrator)
                {
                    var adminResult = MessageBox.Show(
                        "Administrator privileges are required for optimization. Would you like to restart as administrator?",
                        "Administrator Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (adminResult == MessageBoxResult.Yes)
                    {
                        ShowError("Please restart the application as administrator.");
                        return;
                    }
                    else
                    {
                        ShowError("Administrator privileges are required for optimization");
                        return;
                    }
                }

                _isOptimizationInProgress = true;
                ShowProgressPanel();

                // Use enhanced optimization with detailed progress feedback
                await RunEnhancedAutoOptimizationAsync();

                if (_cancellationTokenSource?.Token.IsCancellationRequested != true)
                {
                    ShowSuccess("Optimization completed successfully!\n\nYour PC has been optimized for better performance. You may notice improvements in boot time, application responsiveness, and overall system speed.");
                }
                else
                {
                    ShowInfo("Optimization was cancelled. Some changes may have been applied before cancellation.");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during enhanced auto optimization");
                ShowError($"Auto optimization failed: {ex.Message}");
            }
            finally
            {
                _isOptimizationInProgress = false;
                HideProgressPanel();
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Refresh the current category view
                if (!string.IsNullOrEmpty(_selectedCategory))
                {
                    ShowCategoryOptimizations(_selectedCategory);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error refreshing optimizations");
                ShowError($"Failed to refresh: {ex.Message}");
            }
        }

        private void CategoryTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is TreeViewItem selectedItem)
            {
                var category = selectedItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(category))
                {
                    _selectedCategory = category;
                    ShowCategoryOptimizations(category);
                }
            }
        }

        private void ShowCategoryOptimizations(string category)
        {
            try
            {
                WelcomePanel.Visibility = Visibility.Collapsed;
                SelectedOptimizationsPanel.Visibility = Visibility.Visible;

                var categoryOptimizations = _allOptimizations
                    .Where(o => string.Equals(o.Category, category, StringComparison.OrdinalIgnoreCase) ||
                               IsSubcategoryMatch(category, o))
                    .OrderBy(o => o.Name)
                    .ToList();

                _currentOptimizations = categoryOptimizations;
                SelectedOptimizationsList.ItemsSource = categoryOptimizations;

                // Update status text
                StatusText.Text = $"Showing {categoryOptimizations.Count} optimizations for {GetCategoryDisplayName(category)}";

                _logger.Information("Showing {Count} optimizations for category: {Category}",
                    categoryOptimizations.Count, category);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error showing category optimizations: {Category}", category);
                ShowError($"Failed to load category optimizations: {ex.Message}");
            }
        }

        private static bool IsSubcategoryMatch(string category, OptimizationItemViewModel optimization)
        {
            return category.ToLower() switch
            {
                "visual_effects" => optimization.Id?.Contains("visual") == true,
                "cpu_optimization" => optimization.Id?.Contains("cpu") == true || optimization.Id?.Contains("multicore") == true,
                "memory_management" => optimization.Id?.Contains("memory") == true || optimization.Id?.Contains("ram") == true,
                "gaming_mode" => optimization.Id?.Contains("gaming") == true,
                "power_management" => optimization.Id?.Contains("power") == true,
                "startup_programs" => optimization.Id?.Contains("startup") == true,
                "windows_updates" => optimization.Id?.Contains("update") == true,
                "telemetry" => optimization.Id?.Contains("telemetry") == true,
                "ssd_optimization" => optimization.Id?.Contains("ssd") == true,
                "hdd_optimization" => optimization.Id?.Contains("hdd") == true,
                "disk_cleanup" => optimization.Id?.Contains("cleanup") == true || optimization.Id?.Contains("temporary") == true,
                "cache_management" => optimization.Id?.Contains("cache") == true,
                "intel_cpu" => optimization.Id?.Contains("intel") == true,
                "amd_cpu" => optimization.Id?.Contains("amd") == true && optimization.Id?.Contains("cpu") == true,
                "nvidia_gpu" => optimization.Id?.Contains("nvidia") == true,
                "amd_gpu" => optimization.Id?.Contains("amd") == true && optimization.Id?.Contains("gpu") == true,
                "web_browsers" => optimization.Id?.Contains("chrome") == true || optimization.Id?.Contains("firefox") == true || optimization.Id?.Contains("edge") == true,
                "browser_optimization" => optimization.Id?.Contains("performance_optimize") == true && (optimization.Id?.Contains("chrome") == true || optimization.Id?.Contains("edge") == true),
                "office_suite" => optimization.Id?.Contains("office") == true,
                "gaming_platforms" => optimization.Id?.Contains("steam") == true || optimization.Id?.Contains("epic") == true || optimization.Id?.Contains("origin") == true,
                "development_tools" => optimization.Id?.Contains("vs") == true || optimization.Id?.Contains("jetbrains") == true,
                "temporary_files" => optimization.Id?.Contains("temporary") == true || optimization.Id?.Contains("cache") == true,
                "registry_cleanup" => optimization.Id?.Contains("registry") == true,
                "browser_cache" => optimization.Id?.Contains("cache") == true && (optimization.Id?.Contains("chrome") == true || optimization.Id?.Contains("firefox") == true || optimization.Id?.Contains("edge") == true),
                "system_logs" => optimization.Id?.Contains("log") == true,
                _ => false
            };
        }

        private static string GetCategoryDisplayName(string category)
        {
            return category switch
            {
                "Performance" => "🎯 Performance",
                "System" => "🔧 System",
                "Storage" => "💾 Storage",
                "Hardware" => "🖥️ Hardware",
                "Browser" => "🌐 Browser",
                "Office" => "📝 Office",
                "Gaming" => "🎮 Gaming",
                "Development" => "💻 Development",
                "Cleanup" => "🧹 Cleanup",
                "Power" => "⚡ Power",
                "Startup" => "🚀 Startup",
                "UI Responsiveness" => "🚀 UI Responsiveness",
                "File Operations" => "📁 File Operations",
                "Window Management" => "🪟 Window Management",
                "Memory & System" => "🧠 Memory & System",
                _ => category
            };
        }

        private static string GetCategoryDescription(string category)
        {
            return category switch
            {
                "Performance" => "Optimize system performance with visual effects, CPU, and memory management tweaks.",
                "System" => "Configure system-level settings for better performance and reduced resource usage.",
                "Storage" => "Optimize storage devices for better performance and longevity.",
                "Hardware" => "Hardware-specific optimizations for Intel/AMD CPUs and GPUs.",
                "Software" => "Optimize commonly used software applications for better performance.",
                "Cleanup" => "Clean up temporary files, cache, and unnecessary data to free up space.",
                "visual_effects" => "Optimize Windows visual effects for better performance.",
                "cpu_optimization" => "Optimize CPU settings for maximum performance.",
                "memory_management" => "Optimize memory usage and virtual memory settings.",
                "gaming_mode" => "Enable gaming-specific optimizations for better gaming performance.",
                "power_management" => "Configure power settings for optimal performance.",
                "startup_programs" => "Manage startup programs to improve boot times.",
                "windows_updates" => "Optimize Windows Update settings for better performance.",
                "telemetry" => "Manage Windows telemetry and data collection settings.",
                "ssd_optimization" => "Optimize SSD settings for better performance and longevity.",
                "hdd_optimization" => "Optimize traditional hard drive settings.",
                "disk_cleanup" => "Clean up disk space by removing unnecessary files.",
                "cache_management" => "Manage system and application caches.",
                "intel_cpu" => "Intel CPU-specific optimizations including Turbo Boost and SpeedStep.",
                "amd_cpu" => "AMD CPU-specific optimizations including Precision Boost and Cool'n'Quiet.",
                "nvidia_gpu" => "NVIDIA GPU-specific optimizations and settings.",
                "amd_gpu" => "AMD GPU-specific optimizations and settings.",
                "web_browsers" => "Optimize web browsers for better performance and reduced resource usage.",
                "browser_optimization" => "Advanced browser performance optimizations for faster scrolling, rendering, and tab switching.",
                "office_suite" => "Optimize Microsoft Office and other productivity software.",
                "gaming_platforms" => "Optimize gaming platforms like Steam, Epic Games, and Origin.",
                "development_tools" => "Optimize development tools and IDEs for better performance.",
                "temporary_files" => "Remove temporary files and system cache to free up space.",
                "registry_cleanup" => "Clean up Windows registry for better performance.",
                "browser_cache" => "Clear web browser caches and temporary files.",
                "system_logs" => "Clean up system logs and event viewer data.",
                _ => "Select an optimization to view details and apply changes."
            };
        }

        private void SearchBox_TextChanged(ModernWpf.Controls.AutoSuggestBox sender, ModernWpf.Controls.AutoSuggestBoxTextChangedEventArgs args)
        {
            var searchText = SearchBox.Text?.ToLower() ?? string.Empty;

            if (string.IsNullOrWhiteSpace(searchText))
            {
                // Show all optimizations in current category
                if (!string.IsNullOrEmpty(_selectedCategory))
                {
                    ShowCategoryOptimizations(_selectedCategory);
                }
                return;
            }

            try
            {
                var filteredOptimizations = _allOptimizations
                    .Where(o => o.Name.ToLower().Contains(searchText) ||
                               o.Description.ToLower().Contains(searchText) ||
                               o.Category.ToLower().Contains(searchText))
                    .OrderBy(o => o.Name)
                    .ToList();

                _currentOptimizations = filteredOptimizations;
                SelectedOptimizationsList.ItemsSource = filteredOptimizations;

                // Update UI for search results
                if (filteredOptimizations.Any())
                {
                    StatusText.Text = $"🔍 Search Results ({filteredOptimizations.Count} found) for '{SearchBox.Text}'";
                    WelcomePanel.Visibility = Visibility.Collapsed;
                    SelectedOptimizationsPanel.Visibility = Visibility.Visible;
                }
                else
                {
                    StatusText.Text = $"🔍 No Results Found for '{SearchBox.Text}'";
                    WelcomePanel.Visibility = Visibility.Collapsed;
                    SelectedOptimizationsPanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error filtering optimizations by search: {SearchText}", searchText);
            }
        }

        private async void ApplyAllButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isOptimizationInProgress)
            {
                ShowError("Optimization is already in progress");
                return;
            }

            try
            {
                var applicableOptimizations = _currentOptimizations
                    .Where(o => o.IsApplicable && !o.IsApplied)
                    .ToList();

                if (!applicableOptimizations.Any())
                {
                    ShowInfo("No applicable optimizations found in this category.");
                    return;
                }

                var confirmResult = MessageBox.Show(
                    $"Apply {applicableOptimizations.Count} optimizations in this category?\n\nThis will make changes to your system. A backup will be created automatically.",
                    "Confirm Apply All",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (confirmResult != MessageBoxResult.Yes)
                    return;

                if (!_adminPrivilegeService.IsRunningAsAdministrator)
                {
                    var adminResult = MessageBox.Show(
                        "Administrator privileges are required for optimization. Would you like to restart as administrator?",
                        "Administrator Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (adminResult == MessageBoxResult.Yes)
                    {
                        ShowError("Please restart the application as administrator.");
                        return;
                    }
                    else
                    {
                        ShowError("Administrator privileges are required for optimization");
                        return;
                    }
                }

                _isOptimizationInProgress = true;
                ShowProgressPanel();

                var optimizationIds = applicableOptimizations.Select(o => o.Id).Where(id => !string.IsNullOrEmpty(id)).ToList();
                var success = await _optimizationService.ApplyMultipleOptimizationsAsync(optimizationIds);

                if (success)
                {
                    ShowSuccess($"Successfully applied {optimizationIds.Count} optimizations!");
                    ShowCategoryOptimizations(_selectedCategory);
                }
                else
                {
                    ShowError("Some optimizations failed to apply. Check the logs for details.");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying all optimizations");
                ShowError($"Failed to apply optimizations: {ex.Message}");
            }
            finally
            {
                _isOptimizationInProgress = false;
                HideProgressPanel();
            }
        }

        private void OptimizationItem_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is string optimizationId)
            {
                var optimization = _currentOptimizations.FirstOrDefault(o => o.Id == optimizationId);
                if (optimization != null)
                {
                    optimization.IsSelected = !optimization.IsSelected;

                    // Refresh the items control
                    SelectedOptimizationsList.ItemsSource = null;
                    SelectedOptimizationsList.ItemsSource = _currentOptimizations;
                }
            }
        }

        private async void OptimizationAction_Click(object sender, RoutedEventArgs e)
        {
            if (_isOptimizationInProgress)
            {
                ShowError("Optimization is already in progress");
                return;
            }

            if (sender is not Button button || button.Tag is not string optimizationId)
                return;

            try
            {
                var optimization = _currentOptimizations.FirstOrDefault(o => o.Id == optimizationId);
                if (optimization == null)
                    return;

                if (!await CheckAdminPrivilegesAsync())
                    return;

                await ApplyOptimizationAsync(button, optimization, optimizationId);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying optimization: {OptimizationId}", optimizationId);
                ShowError($"Failed to apply optimization: {ex.Message}");
            }
            finally
            {
                _isOptimizationInProgress = false;
                button.IsEnabled = true;
                button.Content = "Apply";
            }
        }

        private Task<bool> CheckAdminPrivilegesAsync()
        {
            if (_adminPrivilegeService.IsRunningAsAdministrator)
                return Task.FromResult(true);

            var checkResult = MessageBox.Show(
                "Administrator privileges are required for optimization. Would you like to restart as administrator?",
                "Administrator Required",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (checkResult == MessageBoxResult.Yes)
            {
                ShowError("Please restart the application as administrator.");
                return Task.FromResult(false);
            }

            ShowError("Administrator privileges are required for optimization");
            return Task.FromResult(false);
        }

        private async Task ApplyOptimizationAsync(Button button, OptimizationItemViewModel optimization, string optimizationId)
        {
            _isOptimizationInProgress = true;
            button.IsEnabled = false;
            button.Content = "Applying...";

            try
            {
                // Show AI explanation during optimization - this is a new optimization
                ShowProgressPanel();
                await ShowAIExplanationAsync(optimizationId, "analysis", isNewOptimization: true);

                // Add a small delay for the user to read the analysis
                await Task.Delay(2000);

                // Start execution phase - append to existing text
                await ShowAIExplanationAsync(optimizationId, "execution", isNewOptimization: false);

                bool success;
                if (optimization.IsApplied && optimization.IsReversible)
                {
                    success = await _optimizationService.RevertOptimizationAsync(optimizationId);
                    ShowResult(success, $"Successfully reverted: {optimization.Name}", $"Failed to revert: {optimization.Name}");
                }
                else
                {
                    success = await _optimizationService.ApplyOptimizationAsync(optimizationId);
                    ShowResult(success, $"Successfully applied: {optimization.Name}", $"Failed to apply: {optimization.Name}");
                }

                if (success)
                {
                    // Show completion explanation - append to existing text
                    await ShowAIExplanationAsync(optimizationId, "completion", isNewOptimization: false);
                    await Task.Delay(1500); // Let user read completion message

                    // Refresh completed, show current category if selected
                    if (!string.IsNullOrEmpty(_selectedCategory))
                    {
                        ShowCategoryOptimizations(_selectedCategory);
                    }
                }
            }
            finally
            {
                _isOptimizationInProgress = false;
                button.IsEnabled = true;
                button.Content = optimization.IsApplied ? "Revert" : "Apply";
                HideProgressPanel();
            }
        }

        private static void ShowResult(bool success, string successMessage, string errorMessage)
        {
            if (success)
                ShowSuccess(successMessage);
            else
                ShowError(errorMessage);
        }

        private void UpdateProgressFeedback(string operation, int current, int total, string? registryChange = null)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // Update progress bar and counters
                    DetailedProgressBar.Value = total > 0 ? (double)current / total * 100 : 0;
                    ProgressDetailsText.Text = $"{current} / {total} optimizations completed";

                    // Update simple explanation
                    if (_simpleExplanations.TryGetValue(operation, out var simpleText))
                    {
                        // SimpleExplanationText.Text = simpleText; // TODO: Update when AI control is integrated
                    }
                    else
                    {
                        // SimpleExplanationText.Text = $"Working on {operation} optimization..."; // TODO: Update when AI control is integrated
                    }

                    // Update technical explanation
                    if (_technicalExplanations.TryGetValue(operation, out var technicalText))
                    {
                        TechnicalExplanationText.Text = technicalText;
                    }
                    else
                    {
                        TechnicalExplanationText.Text = $"Executing {operation} optimization procedures...";
                    }

                    // Update current operation
                    CurrentOperationText.Text = operation.Replace("_", " ").ToTitleCase();

                    // Add registry change if provided
                    if (!string.IsNullOrEmpty(registryChange))
                    {
                        _registryChanges.Add($"[{DateTime.Now:HH:mm:ss}] {registryChange}");

                        // Keep only last 10 entries
                        if (_registryChanges.Count > 10)
                        {
                            _registryChanges.RemoveAt(0);
                        }

                        RegistryChangesText.Text = string.Join("\n", _registryChanges);

                        // Scroll to bottom
                        RegistryChangesScrollViewer.ScrollToBottom();
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error updating progress feedback");
            }
        }

        private void ShowProgressPanel()
        {
            try
            {
                WelcomePanel.Visibility = Visibility.Collapsed;
                SelectedOptimizationsPanel.Visibility = Visibility.Collapsed;
                WelcomePanel.Visibility = Visibility.Visible;
                AIExplanationPanel.Visibility = Visibility.Visible;

                // Reset progress state
                DetailedProgressBar.Value = 0;
                ProgressRing.IsActive = true;
                _registryChanges.Clear();
                RegistryChangesText.Text = "No registry changes yet...";
                TechnicalDetailsExpander.IsExpanded = false;

                // Initialize cancellation token
                _cancellationTokenSource = new System.Threading.CancellationTokenSource();

                // Start progress updates
                UpdateProgressFeedback("preparing", 0, 1);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error showing progress panel");
            }
        }

        private void HideProgressPanel()
        {
            try
            {
                AIExplanationPanel.Visibility = Visibility.Collapsed;
                WelcomePanel.Visibility = Visibility.Visible;
                ProgressRing.IsActive = false;

                // Clean up cancellation token
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error hiding progress panel");
            }
        }

        private void CancelOptimizationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _cancellationTokenSource?.Cancel();

                // SimpleExplanationText.Text = "Canceling optimization... Please wait while we safely stop the process."; // TODO: Update when AI control is integrated
                // TechnicalExplanationText.Text = "Sending cancellation signal to optimization service and cleaning up partial changes."; // TODO: Update when AI control is integrated  
                // CurrentOperationText.Text = "Cancellation in progress"; // TODO: Update when AI control is integrated

                CancelOptimizationButton.IsEnabled = false;
                CancelOptimizationButton.Content = "Canceling...";

                _logger.Information("User requested optimization cancellation");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error canceling optimization");
                ShowError($"Failed to cancel optimization: {ex.Message}");
            }
        }

        private async Task RunEnhancedAutoOptimizationAsync()
        {
            try
            {
                _logger.Information("Starting AI-powered auto optimization");

                // Initialize AI explanation system
                await InitializeAIExplanationSystemAsync();

                // Define the specific optimizations that should be applied
                var optimizationIds = new List<string>
                {
                    "visual_effects_performance",
                    "startup_programs_cleanup",
                    "temporary_files_cleanup",
                    "power_high_performance",
                    "ssd_trim_enable",
                    "ssd_superfetch_disable",
                    "gaming_mode_enable",
                    "multicore_cpu_scheduling",
                    "high_ram_virtual_memory"
                };

                var totalSteps = optimizationIds.Count + 3; // +3 for prepare, backup, finalize
                var currentStep = 0;

                // Step 1: Preparation with AI explanation
                UpdateProgressFeedback("preparing", ++currentStep, totalSteps);
                await ShowAIExplanationAsync("preparing", "analysis");
                await Task.Delay(2000);

                // Step 2: Backup with AI explanation
                UpdateProgressFeedback("backup", ++currentStep, totalSteps,
                    "Creating system restore point: PC Optimizer Pro - Auto Optimization");
                await ShowAIExplanationAsync("backup", "execution");
                await Task.Delay(2000);

                // Apply each optimization with AI explanations
                foreach (var optimizationId in optimizationIds)
                {
                    if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                    {
                        _logger.Information("Optimization cancelled by user");
                        break;
                    }

                    var operationType = GetOperationTypeById(optimizationId);
                    var registryPath = GetRegistryPathById(optimizationId);

                    UpdateProgressFeedback(operationType, ++currentStep, totalSteps,
                        $"Applying {optimizationId}: {registryPath}");

                    // Show AI explanation for this specific optimization - each optimization is treated as new
                    await ShowAIExplanationAsync(optimizationId, "analysis", isNewOptimization: true);
                    await Task.Delay(1500); // Let user read analysis

                    await ShowAIExplanationAsync(optimizationId, "execution", isNewOptimization: false);

                    try
                    {
                        var success = await _optimizationService.ApplyOptimizationAsync(optimizationId);

                        if (success)
                        {
                            await ShowAIExplanationAsync(optimizationId, "completion", isNewOptimization: false);
                            _logger.Information("Successfully applied optimization: {OptimizationId}", optimizationId);
                        }
                        else
                        {
                            _logger.Warning("Failed to apply optimization: {OptimizationId}", optimizationId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error applying optimization: {OptimizationId}", optimizationId);
                    }

                    await Task.Delay(2000); // Allow user to read the completion feedback
                }

                // Final step: Finalization with AI explanation
                if (_cancellationTokenSource?.Token.IsCancellationRequested != true)
                {
                    UpdateProgressFeedback("finalizing", totalSteps, totalSteps,
                        "Optimization completed - validating system changes");
                    await ShowAIExplanationAsync("finalizing", "completion");
                    await Task.Delay(2000);
                }

                _logger.Information("AI-powered auto optimization completed");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during AI-powered auto optimization");
                throw;
            }
        }

        private static string GetOperationTypeById(string optimizationId)
        {
            return optimizationId switch
            {
                "visual_effects_performance" => "visual_effects",
                "startup_programs_cleanup" => "startup_programs",
                "temporary_files_cleanup" => "disk_cleanup",
                "power_high_performance" => "power_management",
                "ssd_trim_enable" => "ssd_optimization",
                "ssd_superfetch_disable" => "ssd_optimization",
                "gaming_mode_enable" => "gaming_mode",
                "multicore_cpu_scheduling" => "cpu_optimization",
                "high_ram_virtual_memory" => "memory_management",
                _ => "general"
            };
        }

        private static string GetRegistryPathById(string optimizationId)
        {
            return optimizationId switch
            {
                "visual_effects_performance" => "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects",
                "startup_programs_cleanup" => "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "temporary_files_cleanup" => "File system cleanup operations",
                "power_high_performance" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Power",
                "ssd_trim_enable" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\FileSystem",
                "ssd_superfetch_disable" => "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SysMain",
                "gaming_mode_enable" => "HKCU\\SOFTWARE\\Microsoft\\GameBar",
                "multicore_cpu_scheduling" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl",
                "high_ram_virtual_memory" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management",
                _ => "System configuration"
            };
        }

        private static string GetOperationType(OptimizationItem optimization)
        {
            var id = optimization.Id?.ToLower() ?? "";

            if (id.Contains("visual")) return "visual_effects";
            if (id.Contains("cpu") || id.Contains("processor")) return "cpu_optimization";
            if (id.Contains("memory") || id.Contains("ram")) return "memory_management";
            if (id.Contains("startup")) return "startup_programs";
            if (id.Contains("registry")) return "registry_cleanup";
            if (id.Contains("cleanup") || id.Contains("temp")) return "disk_cleanup";
            if (id.Contains("ssd")) return "ssd_optimization";
            if (id.Contains("power")) return "power_management";
            if (id.Contains("gaming")) return "gaming_mode";
            if (id.Contains("telemetry")) return "telemetry";

            return optimization.Category?.ToLower().Replace(" ", "_") ?? "general";
        }

        private static string GetRegistryPath(OptimizationItem optimization)
        {
            var operationType = GetOperationType(optimization);

            return operationType switch
            {
                "visual_effects" => "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects",
                "cpu_optimization" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl",
                "memory_management" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management",
                "startup_programs" => "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "registry_cleanup" => "Multiple registry locations",
                "disk_cleanup" => "File system operations",
                "ssd_optimization" => "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SysMain",
                "power_management" => "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Power",
                "gaming_mode" => "HKCU\\SOFTWARE\\Microsoft\\GameBar",
                "telemetry" => "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\DataCollection",
                _ => "System configuration"
            };
        }

        private static void ShowError(string message)
        {
            MessageBox.Show(message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private static void ShowSuccess(string message)
        {
            MessageBox.Show(message, "Success", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private static void ShowInfo(string message)
        {
            MessageBox.Show(message, "Information", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Initializes the AI explanation system with system context
        /// </summary>
        private async Task InitializeAIExplanationSystemAsync()
        {
            try
            {
                _logger.Information("Initializing AI explanation system");
                _systemContext = await _systemAnalysisService.AnalyzeSystemAsync();
                _logger.Information("AI explanation system initialized with context: RAM={TotalRam}GB, CPU cores={CpuCores}, SSD={HasSsd}",
                    _systemContext.TotalRamGB, _systemContext.CpuCores, _systemContext.HasSsd);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to initialize AI explanation system");
                _systemContext = null;
            }
        }

        /// <summary>
        /// Shows an AI-powered explanation for the given optimization
        /// </summary>
        private async Task ShowAIExplanationAsync(string optimizationId, string phase = "execution", bool isNewOptimization = false)
        {
            try
            {
                if (_systemContext == null)
                {
                    await InitializeAIExplanationSystemAsync();
                }

                if (_systemContext == null)
                {
                    _logger.Warning("System context not available, using fallback explanation");
                    await ShowFallbackExplanationAsync(optimizationId, phase, isNewOptimization);
                    return;
                }

                var explanation = _aiExplanationService.GetExplanation(optimizationId, _systemContext);

                var explanationText = phase switch
                {
                    "analysis" => explanation.PreAnalysisText,
                    "execution" => explanation.AnalysisText,
                    "completion" => explanation.CompletionText,
                    _ => explanation.AnalysisText
                };

                // Set the text on the AI explanation control and start typing animation
                await Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Clear text for new optimization, append for same optimization phases
                        if (isNewOptimization)
                        {
                            AIExplanationControl.ClearText();
                            AIExplanationControl.AppendText = false;
                        }
                        else
                        {
                            AIExplanationControl.AppendText = true;
                        }

                        // Set text triggers automatic typing animation
                        AIExplanationControl.Text = explanationText;
                        // Wait for animation to complete if needed
                        await AIExplanationControl.WaitForCompletionAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error updating AI explanation control, falling back to text display");
                        // Fallback to updating status text if AI control fails
                        //AIStatusText.Text = explanationText;
                    }
                });

                _logger.Information("Displayed AI explanation for optimization: {OptimizationId}, phase: {Phase}, newOpt: {IsNew}",
                    optimizationId, phase, isNewOptimization);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error showing AI explanation for optimization: {OptimizationId}", optimizationId);
                await ShowFallbackExplanationAsync(optimizationId, phase, isNewOptimization);
            }
        }

        /// <summary>
        /// Shows a fallback explanation when AI explanation fails
        /// </summary>
        private async Task ShowFallbackExplanationAsync(string optimizationId, string phase, bool isNewOptimization = false)
        {
            try
            {
                var fallbackText = GetFallbackExplanation(optimizationId, phase);

                await Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Set text triggers automatic typing animation
                        AIExplanationControl.Text = fallbackText;
                        // Wait for animation to complete if needed
                        await AIExplanationControl.WaitForCompletionAsync();
                    }
                    catch
                    {
                        // Ultimate fallback - just update status text
                        //AIStatusText.Text = fallbackText;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error showing fallback explanation");
            }
        }

        /// <summary>
        /// Gets a fallback explanation for when AI service is unavailable
        /// </summary>
        private static string GetFallbackExplanation(string optimizationId, string phase)
        {
            var operationType = GetOperationTypeById(optimizationId);

            return phase switch
            {
                "analysis" => $"I'm analyzing your system to determine the best approach for {operationType} optimization...",
                "execution" => $"I'm now applying {operationType} optimization to improve your PC's performance...",
                "completion" => $"Successfully completed {operationType} optimization. Your system should now perform better!",
                _ => $"Working on {operationType} optimization for your system..."
            };
        }

        private static int GetCategoryOrder(string category)
        {
            return category switch
            {
                "Performance" => 1,
                "System" => 2,
                "Storage" => 3,
                "Hardware" => 4,
                "Browser" => 5,
                "Office" => 6,
                "Gaming" => 7,
                "Development" => 8,
                "Cleanup" => 9,
                "Power" => 10,
                "Startup" => 11,
                _ => 99
            };
        }

        private static string GetSafetyIcon(OptimizationSafety safety)
        {
            return safety switch
            {
                OptimizationSafety.Safe => "✅",
                OptimizationSafety.MostlySafe => "⚠️",
                OptimizationSafety.Risky => "⚠️",
                _ => "❓"
            };
        }

        private void SelectAllOptimizations()
        {
            try
            {
                _logger.Information("Selecting all {Count} optimizations by default", _allOptimizations.Count);

                foreach (var optimization in _allOptimizations)
                {
                    optimization.IsSelected = true;
                }

                // Update checkboxes in tree view
                UpdateTreeViewCheckboxes();

                _logger.Information("All optimizations selected successfully");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error selecting all optimizations");
            }
        }

        private void UpdateTreeViewCheckboxes()
        {
            try
            {
                // Update all checkboxes in the tree view to reflect the current selection state
                var treeView = FindName("CategoryTreeView") as TreeView;
                if (treeView == null)
                {
                    _logger.Warning("CategoryTreeView not found, cannot update checkboxes");
                    return;
                }

                foreach (TreeViewItem categoryItem in treeView.Items)
                {
                    foreach (TreeViewItem optimizationItem in categoryItem.Items)
                    {
                        if (optimizationItem.Header is StackPanel panel)
                        {
                            var checkbox = panel.Children.OfType<CheckBox>().FirstOrDefault();
                            if (checkbox != null && checkbox.Tag is string optimizationId)
                            {
                                var optimization = _allOptimizations.FirstOrDefault(o => o.Id == optimizationId);
                                if (optimization != null)
                                {
                                    checkbox.IsChecked = optimization.IsSelected;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error updating tree view checkboxes");
            }
        }

        private async void ApplyOptimizationsButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isOptimizationInProgress)
            {
                ShowError("Optimization is already in progress");
                return;
            }

            var selectedOptimizations = _currentOptimizations.Where(o => o.IsSelected).ToList();
            if (!selectedOptimizations.Any())
            {
                ShowError("Please select at least one optimization to apply");
                return;
            }

            if (!await CheckAdminPrivilegesAsync())
                return;

            try
            {
                _isOptimizationInProgress = true;

                // Show AI explanation panel
                AIExplanationPanel.Visibility = Visibility.Visible;
                WelcomePanel.Visibility = Visibility.Collapsed;

                // Apply selected optimizations
                await ApplyMultipleOptimizationsAsync(selectedOptimizations);

                ShowSuccess($"Successfully applied {selectedOptimizations.Count} optimizations!");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multiple optimizations");
                ShowError($"Failed to apply optimizations: {ex.Message}");
            }
            finally
            {
                _isOptimizationInProgress = false;
                AIExplanationPanel.Visibility = Visibility.Collapsed;
            }
        }

        private async Task ApplyMultipleOptimizationsAsync(List<OptimizationItemViewModel> optimizations)
        {
            var totalCount = optimizations.Count;
            var completedCount = 0;

            foreach (var optimization in optimizations)
            {
                try
                {
                    StatusText.Text = $"Applying optimization {completedCount + 1} of {totalCount}: {optimization.Name}";

                    await ApplyOptimizationInternalAsync(optimization);
                    completedCount++;

                    // Update progress
                    var progress = (double)completedCount / totalCount * 100;
                    DetailedProgressBar.Value = progress;
                    ProgressDetailsText.Text = $"{completedCount} / {totalCount} optimizations completed";
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Failed to apply optimization: {OptimizationId}", optimization.Id);
                    ShowError($"Failed to apply {optimization.Name}: {ex.Message}");
                }
            }
        }

        private async Task ApplyOptimizationInternalAsync(OptimizationItemViewModel optimization)
        {
            // Apply the optimization using the service
            await _optimizationService.ApplyOptimizationAsync(optimization.Id);

            // Update UI
            optimization.IsApplied = true;
            // Note: ActionText is computed from IsApplied and IsReversible

            // Log the action
            _logger.Information("Applied optimization: {OptimizationName} ({OptimizationId})",
                optimization.Name, optimization.Id);
        }
    }

    /// <summary>
    /// ViewModel for optimization items in the UI
    /// </summary>
    public class OptimizationItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public OptimizationSafety Safety { get; set; }
        public OptimizationImpact Impact { get; set; }
        public bool IsApplicable { get; set; }
        public bool IsApplied { get; set; }
        public bool IsReversible { get; set; }
        public string ExpectedImprovement { get; set; } = string.Empty;
        public bool IsSelected { get; set; }

        public string SafetyText => Safety switch
        {
            OptimizationSafety.Safe => "SAFE",
            OptimizationSafety.MostlySafe => "MOSTLY SAFE",
            OptimizationSafety.Moderate => "MODERATE",
            OptimizationSafety.Risky => "RISKY",
            OptimizationSafety.Dangerous => "DANGEROUS",
            _ => "UNKNOWN"
        };

        public string SafetyColor => Safety switch
        {
            OptimizationSafety.Safe => "#27AE60",
            OptimizationSafety.MostlySafe => "#3498DB",
            OptimizationSafety.Moderate => "#F39C12",
            OptimizationSafety.Risky => "#E74C3C",
            OptimizationSafety.Dangerous => "#8E44AD",
            _ => "#95A5A6"
        };

        public string ImpactText => Impact switch
        {
            OptimizationImpact.Low => "LOW",
            OptimizationImpact.Medium => "MEDIUM",
            OptimizationImpact.High => "HIGH",
            _ => "UNKNOWN"
        };

        public string ImpactColor => Impact switch
        {
            OptimizationImpact.Low => "#95A5A6",
            OptimizationImpact.Medium => "#3498DB",
            OptimizationImpact.High => "#E74C3C",
            _ => "#95A5A6"
        };

        public string ActionText => IsApplied && IsReversible ? "Revert" : "Apply";
    }
}

/// <summary>
/// Extension method for string conversion
/// </summary>
public static class StringExtensions
{
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        var words = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        for (int i = 0; i < words.Length; i++)
        {
            if (words[i].Length > 0)
            {
                words[i] = char.ToUpper(words[i][0]) + words[i][1..].ToLower();
            }
        }
        return string.Join(" ", words);
    }
}
