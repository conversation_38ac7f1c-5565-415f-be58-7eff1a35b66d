{"workbench.colorTheme": "Visual Studio Light", "workbench.iconTheme": "vs-seti", "workbench.productIconTheme": "visual-studio-icons", "editor.fontFamily": "'Cascadia Code', 'Fira Code', 'Consolas', 'Courier New', monospace", "editor.fontSize": 14, "editor.fontWeight": "400", "editor.lineHeight": 1.4, "editor.fontLigatures": true, "terminal.integrated.fontFamily": "'Cascadia Code', 'Consolas', 'Courier New', monospace", "terminal.integrated.fontSize": 14, "terminal.integrated.fontWeight": "400", "workbench.colorCustomizations": {"editor.background": "#ffffff", "editor.foreground": "#000000", "editorLineNumber.foreground": "#2b91af", "editorLineNumber.activeForeground": "#0000ff", "editor.selectionBackground": "#add6ff", "editor.selectionHighlightBackground": "#add6ff4d", "editor.wordHighlightBackground": "#add6ff80", "editor.wordHighlightStrongBackground": "#add6ffcc", "sideBar.background": "#f3f3f3", "sideBar.foreground": "#383838", "sideBar.border": "#e5e5e5", "activityBar.background": "#2c2c2c", "activityBar.foreground": "#ffffff", "activityBar.inactiveForeground": "#999999cc", "activityBar.border": "#e5e5e5", "titleBar.activeBackground": "#dddddd", "titleBar.activeForeground": "#333333", "titleBar.inactiveBackground": "#eeeeee", "titleBar.inactiveForeground": "#999999", "titleBar.border": "#e5e5e5", "menubar.selectionBackground": "#e5e5e5", "menubar.selectionForeground": "#333333", "statusBar.background": "#007acc", "statusBar.foreground": "#ffffff", "statusBar.noFolderBackground": "#68217a", "statusBar.debuggingBackground": "#cc6633", "panel.background": "#ffffff", "panel.border": "#e5e5e5", "panelTitle.activeBorder": "#007acc", "panelTitle.activeForeground": "#000000", "panelTitle.inactiveForeground": "#6f6f6f", "editorGroup.border": "#e5e5e5", "editorGroupHeader.tabsBackground": "#f3f3f3", "tab.activeBackground": "#ffffff", "tab.activeForeground": "#333333", "tab.inactiveBackground": "#ececec", "tab.inactiveForeground": "#717171", "tab.border": "#e5e5e5", "tab.activeBorder": "#007acc", "scrollbarSlider.background": "#c2c2c266", "scrollbarSlider.hoverBackground": "#a6a6a6b3", "scrollbarSlider.activeBackground": "#6464648a", "input.background": "#ffffff", "input.foreground": "#000000", "input.border": "#cecece", "dropdown.background": "#ffffff", "dropdown.foreground": "#000000", "dropdown.border": "#cecece", "button.background": "#0e639c", "button.foreground": "#ffffff", "button.hoverBackground": "#1177bb", "list.activeSelectionBackground": "#0078d4", "list.activeSelectionForeground": "#ffffff", "list.inactiveSelectionBackground": "#e5e5e5", "list.hoverBackground": "#f3f3f3", "list.focusBackground": "#d6ebff", "editorBracketMatch.background": "#add6ff4d", "editorBracketMatch.border": "#0078d4", "editor.lineHighlightBackground": "#f0f0f0", "editor.lineHighlightBorder": "#eeeeee", "minimap.background": "#ffffff", "minimapSlider.background": "#c2c2c266", "minimapSlider.hoverBackground": "#a6a6a6b3", "minimapSlider.activeBackground": "#6464648a"}, "editor.lineNumbers": "on", "editor.renderWhitespace": "selection", "editor.renderIndentGuides": true, "editor.highlightActiveIndentGuide": true, "editor.indentSize": 4, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true, "editor.cursorStyle": "line", "editor.cursorWidth": 2, "editor.cursorBlinking": "blink", "editor.selectionHighlight": true, "editor.occurrencesHighlight": true, "editor.smoothScrolling": true, "editor.mouseWheelScrollSensitivity": 1, "editor.fastScrollSensitivity": 5, "editor.scrollBeyondLastLine": false, "editor.minimap.enabled": true, "editor.minimap.side": "right", "editor.minimap.showSlider": "always", "editor.quickSuggestions": {"other": true, "comments": false, "strings": false}, "editor.suggestOnTriggerCharacters": true, "editor.wordBasedSuggestions": true, "editor.parameterHints.enabled": true, "editor.hover.enabled": true, "editor.hover.delay": 300, "editor.matchBrackets": "always", "editor.autoClosingBrackets": "always", "editor.autoClosingQuotes": "always", "editor.autoSurround": "languageDefined", "workbench.editor.enablePreview": true, "workbench.editor.enablePreviewFromQuickOpen": true, "workbench.editor.showTabs": true, "workbench.editor.tabCloseButton": "right", "workbench.editor.tabSizing": "shrink", "workbench.editor.wrapTabs": false, "workbench.activityBar.visible": true, "workbench.statusBar.visible": true, "workbench.sideBar.location": "left", "workbench.panel.defaultLocation": "bottom", "workbench.startupEditor": "welcomePage", "workbench.editor.restoreViewState": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.cursorStyle": "line", "terminal.integrated.cursorBlinking": true, "terminal.integrated.scrollback": 10000, "terminal.integrated.shell.windows": "pwsh.exe", "explorer.confirmDelete": true, "explorer.confirmDragAndDrop": true, "explorer.openEditors.visible": 10, "explorer.autoReveal": true, "explorer.sortOrder": "type", "search.showLineNumbers": true, "search.smartCase": true, "search.globalFindClipboard": false, "problems.decorations.enabled": true, "problems.showCurrentInStatus": true, "breadcrumbs.enabled": true, "breadcrumbs.showFiles": true, "breadcrumbs.showSymbols": true, "git.enableSmartCommit": true, "git.confirmSync": false, "git.autofetch": true, "scm.diffDecorations": "all", "scm.diffDecorationsGutterWidth": 3, "[csharp]": {"editor.defaultFormatter": "ms-dotnettools.csharp", "editor.formatOnSave": true, "editor.formatOnType": true, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.wordWrap": "off"}, "[xml]": {"editor.defaultFormatter": "ms-dotnettools.vscode-dotnet-runtime", "editor.formatOnSave": true, "editor.tabSize": 2, "editor.insertSpaces": true}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.formatOnSave": true, "editor.tabSize": 2, "editor.insertSpaces": true}, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableImportCompletion": true, "omnisharp.enableRoslynAnalyzers": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true, "editor.lightbulb.enabled": true, "editor.codeLens": true, "editor.folding": true, "editor.foldingStrategy": "indentation", "editor.showFoldingControls": "mouseover", "editor.unfoldOnClickAfterEndOfLine": false, "github.copilot.chat.welcomeMessage": "never", "github.copilot.enable": {"*": true, "yaml": false, "plaintext": false, "markdown": false}, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/node_modules": true, "**/.git": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/node_modules": true}, "window.titleBarStyle": "custom", "window.menuBarVisibility": "toggle", "window.zoomLevel": 0, "editor.mouseWheelZoom": true, "errorLens.enabled": true, "errorLens.enabledDiagnosticLevels": ["error", "warning", "info"], "errorLens.fontSize": "12", "errorLens.fontWeight": "400", "errorLens.fontStyleItalic": true, "errorLens.margin": "2ch", "errorLens.scrollbarHackEnabled": true}