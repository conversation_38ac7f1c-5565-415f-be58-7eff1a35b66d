using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using System.Collections.ObjectModel;
using System.Windows.Threading;

namespace PCOptimizerApp.ViewModels
{
    /// <summary>
    /// Main window ViewModel serving as the primary application controller and data provider.
    /// Implements MVVM pattern using CommunityToolkit.Mvvm for property change notifications and command binding.
    /// Manages application navigation, system monitoring, optimization operations, and real-time data updates.
    /// Coordinates multiple services to provide comprehensive system information and optimization capabilities.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: main viewmodel, MVVM controller, navigation management, system monitoring, optimization commands
    /// Central ViewModel controlling the main application window and orchestrating all major functionality
    /// </remarks>
    public partial class MainWindowViewModel : ObservableObject
    {
        // Service dependencies injected through constructor for testability and loose coupling
        private readonly ISystemInfoService _systemInfoService;
        private readonly IOptimizationService _optimizationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IBackupService _backupService;

        /// <summary>
        /// Timer for periodic updates of system information and performance metrics.
        /// Ensures UI stays current with system changes and resource utilization.
        /// </summary>
        private readonly DispatcherTimer _updateTimer;

        /// <summary>
        /// Event raised when navigation to a different view is requested.
        /// Used by the main window to coordinate view changes and page transitions.
        /// </summary>
        public event EventHandler<string>? NavigationRequested;

        /// <summary>
        /// Current active view name for navigation state tracking.
        /// Determines which page/view is currently displayed in the main content area.
        /// Bound to UI for navigation highlighting and state management.
        /// </summary>
        [ObservableProperty]
        private string _currentView = "ModernOptimization";

        /// <summary>
        /// Comprehensive system information including hardware specs and current status.
        /// Updated periodically and after system changes to reflect current system state.
        /// Bound to UI components displaying system specifications and capabilities.
        /// </summary>
        [ObservableProperty]
        private SystemInfo? _systemInfo;

        /// <summary>
        /// Real-time performance metrics including CPU, memory, and disk utilization.
        /// Updated frequently to provide live system monitoring capabilities.
        /// Bound to performance charts and monitoring displays in the UI.
        /// </summary>
        [ObservableProperty]
        private PerformanceMetrics? _currentMetrics;

        /// <summary>
        /// System health assessment with overall score and detailed factor analysis.
        /// Calculated based on multiple system aspects and performance indicators.
        /// Used to provide users with overall system condition and improvement recommendations.
        /// </summary>
        [ObservableProperty]
        private SystemHealthScore? _healthScore;

        /// <summary>
        /// Property change handler for HealthScore to trigger dependent property updates.
        /// Ensures SystemHealthScore string property is updated when the health score changes.
        /// </summary>
        /// <param name="value">New health score value</param>
        partial void OnHealthScoreChanged(SystemHealthScore? value)
        {
            // Trigger update of the computed SystemHealthScore string property
            OnPropertyChanged(nameof(SystemHealthScore));
        }

        /// <summary>
        /// Human-readable system health status derived from the overall health score.
        /// Provides categorical health assessment for easy user understanding.
        /// Maps numeric scores to descriptive categories (Excellent, Good, Fair, Poor, Critical).
        /// </summary>
        public string SystemHealthScore => HealthScore?.OverallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 60 => "Fair",
            >= 40 => "Poor",
            _ => "Critical"
        };

        /// <summary>
        /// Indicates whether the application is currently performing a loading operation.
        /// Used to show progress indicators and disable UI during long-running operations.
        /// Bound to loading overlays and progress displays throughout the application.
        /// </summary>
        [ObservableProperty]
        private bool _isLoading = false;

        /// <summary>
        /// Descriptive message about the current loading operation.
        /// Provides user feedback about what specific operation is being performed.
        /// Displayed in loading screens and progress indicators for user awareness.
        /// </summary>
        [ObservableProperty]
        private string _loadingMessage = "";

        /// <summary>
        /// Collection of navigation items for the main application menu.
        /// Populated with all available views and their display information.
        /// Bound to navigation controls for user interface generation.
        /// </summary>
        public ObservableCollection<NavigationItem> NavigationItems { get; } = new();

        /// <summary>
        /// Initializes the MainWindowViewModel with required service dependencies.
        /// Sets up dependency injection, initializes navigation items, and configures periodic update timer.
        /// Does NOT perform heavy initialization immediately - InitializeAsync() must be called separately.
        /// </summary>
        /// <param name="systemInfoService">Service for system information gathering and hardware analysis</param>
        /// <param name="optimizationService">Service for applying system optimizations and performance improvements</param>
        /// <param name="performanceMonitoringService">Service for real-time performance monitoring</param>
        /// <param name="backupService">Service for creating and managing system backups</param>
        /// <remarks>
        /// AI Search Keywords: viewmodel constructor, dependency injection, service initialization, MVVM setup
        /// </remarks>
        public MainWindowViewModel(
            ISystemInfoService systemInfoService,
            IOptimizationService optimizationService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBackupService backupService)
        {
            // Store service dependencies for use throughout ViewModel lifetime
            _systemInfoService = systemInfoService;
            _optimizationService = optimizationService;
            _performanceMonitoringService = performanceMonitoringService;
            _backupService = backupService;

            // Initialize navigation menu items for the main application interface
            InitializeNavigationItems();

            // Configure periodic update timer for system metrics (5-second intervals)
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _updateTimer.Tick += UpdateTimer_Tick;

            // Note: Heavy initialization is deferred to InitializeAsync() to avoid blocking UI construction
        }

        /// <summary>
        /// Initializes the navigation menu items for the main application interface.
        /// Populates the NavigationItems collection with all available views and their metadata.
        /// Each item includes display name, icon, and target view identifier for navigation.
        /// </summary>
        /// <remarks>
        /// AI Search Keywords: navigation setup, menu initialization, application views, navigation items
        /// </remarks>
        private void InitializeNavigationItems()
        {
            NavigationItems.Clear();

            // Add only the Modern Optimization view - the primary interface for PC optimization
            NavigationItems.Add(new NavigationItem("Modern Optimization", "🎯", "ModernOptimization"));
        }

        /// <summary>
        /// Performs asynchronous initialization of the ViewModel including system analysis and monitoring setup.
        /// This method performs operations in the background without blocking the UI or showing loading states.
        /// Loads system information, calculates health scores, starts performance monitoring, and begins periodic updates.
        /// </summary>
        /// <returns>Task representing the asynchronous initialization operation</returns>
        /// <remarks>
        /// AI Search Keywords: async initialization, background loading, performance monitoring setup, non-blocking initialization
        /// Runs in background after UI is already displayed to avoid blocking user interaction
        /// </remarks>
        public async Task InitializeAsync()
        {
            try
            {
                // No loading states - run everything in background

                // Load comprehensive system information including hardware specifications
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();

                // Calculate overall system health based on multiple performance factors
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();

                // Get current performance snapshot for immediate display
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();

                // Subscribe to real-time performance updates for continuous monitoring
                _performanceMonitoringService.PerformanceUpdated += OnPerformanceUpdated;
                _performanceMonitoringService.StartMonitoring();

                // Begin periodic updates of system information and health metrics
                _updateTimer.Start();
            }
            catch (Exception ex)
            {
                // Handle initialization errors gracefully without showing error to user
                System.Diagnostics.Debug.WriteLine($"Background initialization error: {ex.Message}");

                // Set default values so UI still works
                SystemInfo = new SystemInfo { OperatingSystem = "Windows", ProcessorName = "Unknown" };
                HealthScore = new SystemHealthScore { OverallScore = 70 };
                CurrentMetrics = new PerformanceMetrics();
            }
        }

        /// <summary>
        /// Event handler for real-time performance metric updates from the monitoring service.
        /// Ensures UI updates occur on the main thread for proper data binding and display.
        /// Provides continuous performance data refresh for live monitoring displays.
        /// </summary>
        /// <param name="sender">Source of the performance update event (monitoring service)</param>
        /// <param name="metrics">Updated performance metrics with current system resource utilization</param>
        /// <remarks>
        /// AI Search Keywords: performance updates, real-time monitoring, UI thread marshaling, live data updates
        /// </remarks>
        private void OnPerformanceUpdated(object? sender, PerformanceMetrics metrics)
        {
            // Marshal UI updates to the main dispatcher thread for thread safety
            App.Current.Dispatcher.Invoke(() =>
            {
                CurrentMetrics = metrics;
            });
        }

        /// <summary>
        /// Timer event handler for periodic updates of system information and health metrics.
        /// Refreshes system data every 5 seconds to keep the UI current with system changes.
        /// Handles errors gracefully to prevent timer stoppage due to transient failures.
        /// </summary>
        /// <param name="sender">Timer object that triggered the update</param>
        /// <param name="e">Timer event arguments</param>
        /// <remarks>
        /// AI Search Keywords: periodic updates, timer handler, system refresh, scheduled updates
        /// </remarks>
        private async void UpdateTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // Only perform updates when not already in a loading state
                if (!IsLoading)
                {
                    // Refresh current performance metrics for live monitoring
                    CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();

                    // Recalculate system health score to reflect current conditions
                    HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                }
            }
            catch (Exception ex)
            {
                // Log errors but continue timer operation to maintain periodic updates
                System.Diagnostics.Debug.WriteLine($"Error updating performance metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// RelayCommand for navigation between different views in the application.
        /// Updates the current view state and triggers navigation events for the main window.
        /// Bound to navigation menu items and buttons throughout the user interface.
        /// </summary>
        /// <param name="viewName">Name of the target view to navigate to</param>
        /// <remarks>
        /// AI Search Keywords: navigation command, view switching, application navigation, MVVM navigation
        /// </remarks>
        [RelayCommand]
        private void NavigateToView(string viewName)
        {
            // Update current view tracking for UI state management
            CurrentView = viewName;

            // Notify the main window to perform the actual view transition
            NavigationRequested?.Invoke(this, viewName);
        }

        /// <summary>
        /// RelayCommand for performing quick system optimization with automatic optimization selection.
        /// Executes safe, high-impact optimizations automatically without user intervention.
        /// Provides progress feedback and refreshes system information after completion.
        /// </summary>
        /// <returns>Task representing the asynchronous optimization operation</returns>
        /// <remarks>
        /// AI Search Keywords: quick optimize, automatic optimization, safe optimizations, one-click optimization
        /// </remarks>
        [RelayCommand]
        private async Task QuickOptimizeAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Running quick optimization...";

                // Execute intelligent quick optimization with safe settings
                var result = await _optimizationService.RunQuickOptimizeAsync();

                if (result.Success)
                {
                    // Refresh system information to reflect optimization results
                    SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                    HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                }
            }
            catch (Exception ex)
            {
                // Log optimization errors for debugging and user support
                System.Diagnostics.Debug.WriteLine($"Error running quick optimization: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// RelayCommand for manually refreshing all system information and performance data.
        /// Reloads hardware specifications, performance metrics, and health calculations.
        /// Useful for updating data after system changes or troubleshooting display issues.
        /// </summary>
        /// <returns>Task representing the asynchronous refresh operation</returns>
        /// <remarks>
        /// AI Search Keywords: refresh system info, manual update, system data reload, information refresh
        /// </remarks>
        [RelayCommand]
        private async Task RefreshSystemInfoAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Refreshing system information...";

                // Reload all system information components
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
            }
            catch (Exception ex)
            {
                // Log refresh errors for debugging purposes
                System.Diagnostics.Debug.WriteLine($"Error refreshing system information: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CleanDiskAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Cleaning temporary files and disk space...";

                var result = await _optimizationService.ApplyOptimizationAsync("temporary_files_cleanup");

                if (result)
                {
                    LoadingMessage = "Disk cleanup completed successfully!";
                    // Refresh system information to show updated disk space
                    SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                    await Task.Delay(2000); // Show success message for 2 seconds
                }
                else
                {
                    LoadingMessage = "Disk cleanup failed. Please try again.";
                    await Task.Delay(2000);
                }
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error during disk cleanup: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task AnalyzeSystemAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Analyzing system performance and configuration...";

                // Comprehensive system analysis
                LoadingMessage = "Scanning hardware configuration...";
                await Task.Delay(1000);

                LoadingMessage = "Analyzing performance bottlenecks...";
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                await Task.Delay(1000);

                LoadingMessage = "Calculating system health score...";
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                await Task.Delay(1000);

                LoadingMessage = "Generating optimization recommendations...";
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
                await Task.Delay(1000);

                LoadingMessage = "System analysis completed successfully!";
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error during system analysis: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CreateBackupAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Creating system restore point...";

                // Add backup service dependency injection first
                var backupService = new BackupService(); // Temporary - should be injected
                var result = await backupService.CreateSystemRestorePointAsync("PC Optimizer Pro - Pre-optimization backup");

                if (result)
                {
                    LoadingMessage = "System restore point created successfully!";
                    await Task.Delay(2000);
                }
                else
                {
                    LoadingMessage = "Failed to create system restore point.";
                    await Task.Delay(2000);
                }
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error creating backup: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Cleans up resources and stops monitoring services when the ViewModel is disposed.
        /// Properly disposes of timers, event subscriptions, and monitoring services to prevent memory leaks.
        /// Should be called when the main window is closing or the ViewModel is no longer needed.
        /// </summary>
        /// <remarks>
        /// AI Search Keywords: cleanup, resource disposal, memory management, service cleanup, timer disposal
        /// </remarks>
        public void Cleanup()
        {
            // Stop the periodic update timer to prevent further timer events
            _updateTimer?.Stop();

            // Stop performance monitoring to release system resources
            _performanceMonitoringService?.StopMonitoring();

            // Unsubscribe from performance update events to prevent memory leaks
            if (_performanceMonitoringService != null)
            {
                _performanceMonitoringService.PerformanceUpdated -= OnPerformanceUpdated;
            }
        }
    }

    /// <summary>
    /// Navigation item model representing a menu entry in the main application navigation.
    /// Contains display information and target view identification for application navigation.
    /// Used to populate navigation menus and handle view transitions throughout the application.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: navigation item, menu item, navigation model, view navigation, application menu
    /// </remarks>
    public class NavigationItem
    {
        /// <summary>Display name shown in the navigation menu</summary>
        public string Name { get; set; }

        /// <summary>Icon or emoji displayed alongside the navigation item</summary>
        public string Icon { get; set; }

        /// <summary>Internal view name used for navigation routing and view identification</summary>
        public string ViewName { get; set; }

        /// <summary>
        /// Initializes a new navigation item with display and routing information.
        /// </summary>
        /// <param name="name">Human-readable name for the navigation item</param>
        /// <param name="icon">Icon or emoji to display with the navigation item</param>
        /// <param name="viewName">Internal view identifier for navigation routing</param>
        public NavigationItem(string name, string icon, string viewName)
        {
            Name = name;
            Icon = icon;
            ViewName = viewName;
        }
    }
}
