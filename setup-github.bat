@echo off
echo Setting up PCOptimizerApp for GitHub
echo ====================================
echo.

echo Step 1: Checking Git installation...
git --version
if %errorlevel% neq 0 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/win
    echo Then run this script again.
    pause
    exit /b 1
)

echo.
echo Step 2: Configuring Git (if not already configured)...
echo Please enter your GitHub username:
set /p username="Username: "
echo Please enter your GitHub email:
set /p email="Email: "

git config --global user.name "%username%"
git config --global user.email "%email%"

echo.
echo Step 3: Adding all files to Git...
git add .

echo.
echo Step 4: Creating initial commit...
git commit -m "Initial commit: Professional PC Optimizer with AI-powered analysis and live optimization"

echo.
echo Step 5: Instructions for GitHub...
echo.
echo Now you need to:
echo 1. Go to https://github.com/new
echo 2. Create a new repository named "<PERSON>OptimizerApp" or "PCfast"
echo 3. Copy the repository URL (e.g., https://github.com/yourusername/PCOptimizerApp.git)
echo 4. Run these commands:
echo.
echo    git remote add origin [YOUR_REPO_URL]
echo    git branch -M main
echo    git push -u origin main
echo.
echo Example:
echo    git remote add origin https://github.com/%username%/PCOptimizerApp.git
echo    git branch -M main
echo    git push -u origin main
echo.

pause
