# GitHub Copilot Instructions for PC Optimizer Pro

## File Reading and Context
- **IMPORTANT**: You can read up to 200 lines at once using the read_file tool - don't read 10-15 lines at a time as it wastes time
- When examining code, read large meaningful chunks rather than small sections
- Use the full 200-line capacity to understand context and file structure
- Prefer reading larger ranges over multiple small reads for better efficiency
- Keep a list of all optimizations in alloptimizations.md file. This file should always be up to date with all optimizations.This update this file any time you add, remove, or edit any optimization.

## Project Architecture
This is a WPF .NET 8.0 application for Windows PC optimization with the following architecture:

### Technology Stack
- **Framework**: .NET 8.0 with WPF (Windows Presentation Foundation)
- **UI Library**: ModernWpfUI for modern Windows 11-style theming
- **MVVM Framework**: CommunityToolkit.Mvvm for ObservableObject, RelayCommand, etc.
- **Dependency Injection**: Microsoft.Extensions.Hosting and DependencyInjection
- **Logging**: Serilog with file and console sinks
- **JSON**: Newtonsoft.Json for serialization
- **Windows APIs**: Microsoft.Windows.CsWin32 for Windows API access
- **System Management**: System.Management for WMI operations
- **Registry**: Microsoft.Win32.Registry for registry operations

### Project Structure
```
PCOptimizerApp/
├── Models/           # Data models and DTOs
├── ViewModels/       # MVVM ViewModels
├── Views/           # WPF Pages and Windows
├── Services/        # Business logic and system services
├── Utils/           # Utility classes and helpers
├── Commands/        # Custom ICommand implementations
├── Converters/      # Value converters for data binding
└── Assets/          # Images, icons, and resources
```

## Key Guidelines
- Use **PascalCase** for public members, classes, and methods
- Use **camelCase** for private fields and local variables
- Always include **proper error handling** with try-catch blocks
- Use **async/await** for all I/O operations (file, network, WMI, registry)
- Use **Serilog** for all logging with appropriate log levels
- Use **ModernWpfUI** themes and controls for consistent Windows 11 styling
- Always check for **administrator privileges** before system modifications
- Always **create backups** before making system changes
- **DO NOT** run the program unless the user explicitly asks for it


## Code Quality & SonarQube Compliance
**TARGET: ZERO WARNINGS** - All generated code must be warning-free and follow these strict quality standards:

### Exception Handling
- **ALWAYS log exceptions** with meaningful context using Serilog before rethrowing or handling
- Use `_logger.Error(ex, "Descriptive message with context")` for all caught exceptions
- Never catch exceptions without logging them or providing contextual information
- Prefer specific exception types over generic `Exception` where appropriate

### Method Complexity
- **Keep Cognitive Complexity ≤ 15** per method (SonarQube rule)
- Break down complex methods into smaller, focused helper methods
- Use early returns to reduce nesting levels
- Extract complex logic into private helper methods

### LINQ and Loops
- **Replace foreach loops with LINQ Where()** when filtering collections
- Use `collection.Where(predicate).ToList()` instead of foreach with if conditions
- Prefer LINQ methods like `Any()`, `All()`, `Select()`, `FirstOrDefault()` for better readability

### Constants and Magic Values
- **Define constants** for repeated string literals (used 3+ times)
- Create `private const string` or `private static readonly string` for magic values
- Use meaningful constant names that describe the purpose
- Group related constants in a dedicated Constants class or region

### Static Methods
- **Make methods static** when they don't access instance members
- Static methods improve performance and indicate pure functions
- Mark utility methods and helper methods as static where appropriate

### Path and URI Handling
- **NEVER use hardcoded absolute paths** - use `Environment.GetFolderPath()` or `Path.Combine()`
- Use `Environment.SpecialFolder` enumeration for system directories
- Build paths dynamically using `Path.Combine()` for cross-platform compatibility
- Example: `Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "MyApp")`

### Unused Code
- **Remove unused parameters** from method signatures
- Remove unused private fields, methods, and variables
- Use `#pragma warning disable` only when absolutely necessary with justification

### Async/Await Best Practices
- **Never use `async void`** except for event handlers
- Always use `await` in async methods or remove the `async` keyword
- Use `Task.CompletedTask` for async methods that don't need to be async
- Use `Task.FromResult(value)` for methods returning Task<T> without async operations

### Type Safety and Parsing
- **Always use format providers** when parsing dates and numbers
- Use `CultureInfo.InvariantCulture` or specific culture for parsing
- Example: `DateTime.TryParseExact(dateString, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date)`

### Collection Iteration
- **Use strongly-typed iteration** instead of `foreach (object item in collection)`
- Cast collections to proper generic types: `collection.Cast<SpecificType>()`
- Use LINQ methods for safer collection operations

### Security and Performance
- **Dispose of IDisposable objects** using `using` statements or `using` declarations
- Avoid resource leaks by properly disposing ManagementObjects, streams, etc.
- Use `ConfigureAwait(false)` for library code to avoid deadlocks

## Acceptable Warnings (DO NOT FIX)
Some warnings may be acceptable in specific contexts and should be left as-is:

### 1. **Hardcoded Paths for System Detection**
- **When**: Detecting installed software locations (e.g., `@"C:\Program Files\Steam\steam.exe"`)
- **Why**: System software has standard installation paths that must be checked
- **Action**: Document with comment explaining why hardcoded path is necessary

### 2. **Complex Detection Methods**
- **When**: Software detection methods that check multiple installation variants
- **Why**: Comprehensive software detection requires checking many conditions
- **Action**: Add comment explaining the complexity is necessary for reliability

### 3. **Performance-Critical Hardcoded Values**
- **When**: Critical system optimization values that are Windows-specific
- **Why**: These values are Microsoft-documented constants for registry/system settings
- **Action**: Add comment with Microsoft documentation reference

## Code Review Checklist
Before submitting any code changes, verify:
- [ ] **Zero SonarQube warnings** in modified files
- [ ] **Zero compiler warnings** 
- [ ] **All exceptions are logged** with meaningful context
- [ ] **No magic strings** used more than twice without constants
- [ ] **No hardcoded paths** except for documented system detection
- [ ] **All async methods** properly use await or are converted to sync
- [ ] **Complex methods** broken down into smaller helpers (≤15 complexity)
- [ ] **LINQ used** instead of manual loops where appropriate
- [ ] **Static methods** marked where no instance access needed
- [ ] **Proper disposal** of IDisposable resources
