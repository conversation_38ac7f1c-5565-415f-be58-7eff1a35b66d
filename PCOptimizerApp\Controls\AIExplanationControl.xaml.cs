using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using Serilog;

namespace PCOptimizerApp.Controls
{
    /// <summary>
    /// AI-powered explanation control with typing animation
    /// </summary>
    public partial class AIExplanationControl : UserControl
    {
        private static readonly ILogger Logger = Log.ForContext<AIExplanationControl>();
        
        private DispatcherTimer? _typingTimer;
        private string _fullText = string.Empty;
        private int _currentCharIndex = 0;
        private double _typingSpeed = 50; // Characters per second (medium speed)
        private bool _isAnimating = false;
        private TaskCompletionSource<bool>? _animationCompleted;

        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(AIExplanationControl),
                new PropertyMetadata(string.Empty, OnTextPropertyChanged));

        public static readonly DependencyProperty TypingSpeedProperty =
            DependencyProperty.Register(nameof(TypingSpeed), typeof(TypingSpeedMode), typeof(AIExplanationControl),
                new PropertyMetadata(TypingSpeedMode.Medium, OnTypingSpeedPropertyChanged));

        public static readonly DependencyProperty AllowSkipProperty =
            DependencyProperty.Register(nameof(AllowSkip), typeof(bool), typeof(AIExplanationControl),
                new PropertyMetadata(true));

        public static readonly DependencyProperty AppendTextProperty =
            DependencyProperty.Register(nameof(AppendText), typeof(bool), typeof(AIExplanationControl),
                new PropertyMetadata(false));

        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        public TypingSpeedMode TypingSpeed
        {
            get => (TypingSpeedMode)GetValue(TypingSpeedProperty);
            set => SetValue(TypingSpeedProperty, value);
        }

        public bool AllowSkip
        {
            get => (bool)GetValue(AllowSkipProperty);
            set => SetValue(AllowSkipProperty, value);
        }

        public bool AppendText
        {
            get => (bool)GetValue(AppendTextProperty);
            set => SetValue(AppendTextProperty, value);
        }

        public AIExplanationControl()
        {
            InitializeComponent();
            MouseLeftButtonDown += OnMouseClick;
            KeyDown += OnKeyDown;
            Focusable = true;
            Unloaded += OnUnloaded;
        }

        private static void OnTextPropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AIExplanationControl control)
            {
                control.StartTypingAnimation((string)e.NewValue);
            }
        }

        private static void OnTypingSpeedPropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AIExplanationControl control)
            {
                control.UpdateTypingSpeed((TypingSpeedMode)e.NewValue);
            }
        }

        private void UpdateTypingSpeed(TypingSpeedMode speed)
        {
            _typingSpeed = speed switch
            {
                TypingSpeedMode.Slow => 30,    // 30 WPM for complex analysis
                TypingSpeedMode.Medium => 60,  // 60 WPM for standard explanations
                TypingSpeedMode.Fast => 90,    // 90 WPM for routine confirmations
                _ => 60
            };
        }

        private void StartTypingAnimation(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                if (!AppendText)
                {
                    DisplayTextBlock.Text = string.Empty;
                }
                return;
            }

            _fullText = text;
            _currentCharIndex = 0;
            _isAnimating = true;
            _animationCompleted = new TaskCompletionSource<bool>();

            // Only clear text if not appending (new optimization)
            if (!AppendText)
            {
                DisplayTextBlock.Text = string.Empty;
            }
            else
            {
                // Add some spacing between phases
                if (!string.IsNullOrEmpty(DisplayTextBlock.Text))
                {
                    DisplayTextBlock.Text += "\n\n";
                }
            }
            
            _typingTimer?.Stop();
            _typingTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(1000.0 / _typingSpeed)
            };
            _typingTimer.Tick += OnTypingTimerTick;
            _typingTimer.Start();

            Logger.Debug("Starting AI explanation animation (append={AppendMode}): {TextPreview}", 
                AppendText, text.Substring(0, Math.Min(50, text.Length)));
        }

        private void OnTypingTimerTick(object? sender, EventArgs e)
        {
            if (_currentCharIndex >= _fullText.Length)
            {
                CompleteAnimationWithDelay();
                return;
            }

            var nextChar = _fullText[_currentCharIndex];
            DisplayTextBlock.Text += nextChar;
            _currentCharIndex++;

            // Add pause for punctuation to simulate natural speech
            if (nextChar == '.' || nextChar == '!' || nextChar == '?')
            {
                _typingTimer!.Interval = TimeSpan.FromMilliseconds(500); // 0.5s pause
            }
            else if (nextChar == ',')
            {
                _typingTimer!.Interval = TimeSpan.FromMilliseconds(200); // 0.2s pause
            }
            else
            {
                _typingTimer!.Interval = TimeSpan.FromMilliseconds(1000.0 / _typingSpeed);
            }
        }

        private async void CompleteAnimationWithDelay()
        {
            try
            {
                _typingTimer?.Stop();
                _isAnimating = false;
                
                // Complete the current text being typed
                if (AppendText)
                {
                    // Add remaining characters to existing text
                    var remainingText = _fullText.Substring(_currentCharIndex);
                    DisplayTextBlock.Text += remainingText;
                }
                else
                {
                    DisplayTextBlock.Text = _fullText;
                }
                
                Logger.Debug("AI explanation animation completed, waiting 1 second before signaling completion");
                
                // Wait 1 second after full text is rendered
                await Task.Delay(1000);
                
                _animationCompleted?.SetResult(true);
                
                Logger.Debug("AI explanation animation fully completed with delay");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error completing AI explanation animation with delay");
                _animationCompleted?.SetException(ex);
            }
        }

        private void OnMouseClick(object sender, RoutedEventArgs e)
        {
            if (_isAnimating && AllowSkip)
            {
                CompleteAnimation();
            }
        }

        private void OnKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if ((e.Key == System.Windows.Input.Key.Space || e.Key == System.Windows.Input.Key.Enter) && _isAnimating && AllowSkip)
            {
                CompleteAnimation();
                e.Handled = true;
            }
        }

        private void CompleteAnimation()
        {
            try
            {
                _typingTimer?.Stop();
                _isAnimating = false;
                
                // Complete the current text being typed
                if (AppendText)
                {
                    // Add remaining characters to existing text
                    var remainingText = _fullText.Substring(_currentCharIndex);
                    DisplayTextBlock.Text += remainingText;
                }
                else
                {
                    DisplayTextBlock.Text = _fullText;
                }
                
                _animationCompleted?.SetResult(true);
                
                Logger.Debug("AI explanation animation completed (user skipped)");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error completing AI explanation animation");
                _animationCompleted?.SetException(ex);
            }
        }

        /// <summary>
        /// Clears all text - used when starting a new optimization
        /// </summary>
        public void ClearText()
        {
            try
            {
                _typingTimer?.Stop();
                _isAnimating = false;
                DisplayTextBlock.Text = string.Empty;
                _animationCompleted?.SetResult(true);
                
                Logger.Debug("AI explanation text cleared for new optimization");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error clearing AI explanation text");
            }
        }

        public Task WaitForCompletionAsync()
        {
            return _animationCompleted?.Task ?? Task.CompletedTask;
        }

        public void StopAnimation()
        {
            if (_isAnimating)
            {
                CompleteAnimation();
            }
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            _typingTimer?.Stop();
            _typingTimer = null;
        }
    }

    public enum TypingSpeedMode
    {
        Slow,    // 30 WPM - for complex analysis
        Medium,  // 60 WPM - for standard explanations  
        Fast     // 90 WPM - for routine confirmations
    }
}
