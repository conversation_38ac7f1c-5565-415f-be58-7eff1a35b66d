<Page x:Class="PCOptimizerApp.Views.ModernOptimizationPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:ui="http://schemas.modernwpf.com/2019"
      xmlns:controls="clr-namespace:PCOptimizerApp.Controls"
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="PC Optimization Center">

    <Page.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumLowBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <!-- Category Header Style -->
        <Style x:Key="CategoryHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Optimization Item Style -->
        <Style x:Key="OptimizationItemStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumLowBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="0,3"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundListMediumBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemControlHighlightBaseMediumBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Primary Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Status Text Style -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseMediumHighBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="TextTrimming" Value="None"/>
            <Setter Property="Margin" Value="0,1"/>
        </Style>

        <!-- Progress Ring Style -->
        <Style x:Key="ModernProgressRingStyle" TargetType="ui:ProgressRing">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Foreground" Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
        </Style>

        <!-- Compact TreeView Item Style -->
        <Style x:Key="CompactCategoryTreeViewItemStyle" TargetType="TreeViewItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
            <Setter Property="Padding" Value="2,1"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Page.Resources>

    <Grid Background="{DynamicResource SystemControlBackgroundBaseLowBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" 
                BorderThickness="0,0,0,1" 
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="PC Optimization Center" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                    <TextBlock Text="Optimize your PC with intelligent, category-based tweaks" 
                               FontSize="14" 
                               Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                               Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="AutoOptimizeButton" 
                            Content="🚀 Auto Optimizations" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            FontSize="14"
                            Margin="0,0,10,0"
                            Click="AutoOptimizeButton_Click"/>
                    <Button x:Name="RefreshButton" 
                            Content="🔄 Refresh" 
                            Style="{StaticResource {x:Type Button}}"
                            Padding="15,8"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400" MinWidth="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Categories -->
            <Border Grid.Column="0" 
                    Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" 
                    BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search Box -->
                    <ui:AutoSuggestBox x:Name="SearchBox" 
                                       Grid.Row="0"
                                       PlaceholderText="Search optimizations..."
                                       Margin="15,15,15,10"
                                       TextChanged="SearchBox_TextChanged"/>

                    <!-- Category TreeView -->
                    <TreeView x:Name="CategoryTreeView" 
                              Grid.Row="1"
                              Background="Transparent"
                              BorderThickness="0"
                              Margin="8,0,8,0"
                              Padding="0"
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                              ScrollViewer.VerticalScrollBarVisibility="Auto"
                              SelectedItemChanged="CategoryTreeView_SelectedItemChanged">
                        <TreeView.ItemContainerStyle>
                            <Style TargetType="TreeViewItem" BasedOn="{StaticResource CompactCategoryTreeViewItemStyle}">
                                <Setter Property="IsExpanded" Value="True"/>
                            </Style>
                        </TreeView.ItemContainerStyle>
                    </TreeView>

                    <!-- Status Bar -->
                    <Border Grid.Row="2" 
                            Background="{DynamicResource SystemControlBackgroundBaseMediumBrush}" 
                            BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" 
                            BorderThickness="0,1,0,0" 
                            Padding="15,10">
                        <TextBlock x:Name="StatusText" 
                                   Text="Ready - Select optimizations to apply"
                                   FontSize="12"
                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
                    </Border>
                </Grid>
            </Border>

            <!-- Right Panel - Details -->
            <Border Grid.Column="1" 
                    Background="{DynamicResource SystemControlBackgroundBaseLowBrush}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" 
                              HorizontalScrollBarVisibility="Disabled">
                    <StackPanel Margin="20">
                        <!-- Welcome Message -->
                        <Border x:Name="WelcomePanel" 
                                Style="{StaticResource ModernCardStyle}">
                            <StackPanel>
                                <TextBlock Text="🚀 Welcome to PC Optimizer Pro" 
                                           FontSize="22" 
                                           FontWeight="Bold"
                                           Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                                <TextBlock Text="Your intelligent system optimization companion" 
                                           FontSize="14" 
                                           Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                           Margin="0,10,0,0"/>
                                <TextBlock Text="Select categories from the left panel to begin optimizing your system. Each optimization is carefully categorized and explained." 
                                           FontSize="12" 
                                           Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                           TextWrapping="Wrap"
                                           Margin="0,10,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Selected Optimizations -->
                        <Border x:Name="SelectedOptimizationsPanel" 
                                Style="{StaticResource ModernCardStyle}"
                                Visibility="Collapsed">
                            <StackPanel>
                                <TextBlock Text="📋 Selected Optimizations" 
                                           FontSize="18" 
                                           FontWeight="Bold"
                                           Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                                
                                <ItemsControl x:Name="SelectedOptimizationsList" 
                                              Margin="0,15,0,0">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource OptimizationItemStyle}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Checkbox -->
                                                    <CheckBox x:Name="OptimizationCheckBox" 
                                                              Grid.Column="0"
                                                              IsChecked="{Binding IsSelected}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,10,0"/>

                                                    <!-- Optimization Info -->
                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding Name}" 
                                                                   FontSize="14" 
                                                                   FontWeight="SemiBold"
                                                                   Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                                                        <TextBlock Text="{Binding Description}" 
                                                                   FontSize="12" 
                                                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                                                   TextWrapping="Wrap"
                                                                   Margin="0,2,0,0"/>
                                                        <TextBlock Text="{Binding ExpectedImprovement}" 
                                                                   FontSize="11" 
                                                                   Foreground="{DynamicResource SystemControlHighlightAccentBrush}"
                                                                   FontStyle="Italic"
                                                                   Margin="0,2,0,0"/>
                                                    </StackPanel>

                                                    <!-- Status Badges -->
                                                    <StackPanel Grid.Column="2" 
                                                                Orientation="Horizontal" 
                                                                VerticalAlignment="Center"
                                                                Margin="10,0">
                                                        <Border Background="{Binding SafetyColor}" 
                                                                CornerRadius="10" 
                                                                Padding="6,2">
                                                            <TextBlock Text="{Binding SafetyText}" 
                                                                       FontSize="10" 
                                                                       Foreground="White"
                                                                       FontWeight="SemiBold"/>
                                                        </Border>
                                                        <Border Background="{Binding ImpactColor}" 
                                                                CornerRadius="10" 
                                                                Padding="6,2"
                                                                Margin="5,0,0,0">
                                                            <TextBlock Text="{Binding ImpactText}" 
                                                                       FontSize="10" 
                                                                       Foreground="White"
                                                                       FontWeight="SemiBold"/>
                                                        </Border>
                                                    </StackPanel>

                                                    <!-- Action Button -->
                                                    <Button Grid.Column="3" 
                                                            Content="{Binding ActionText}" 
                                                            Style="{StaticResource {x:Type Button}}"
                                                            FontSize="11"
                                                            Padding="10,5"
                                                            Tag="{Binding Id}"
                                                            Click="OptimizationAction_Click"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <!-- Apply Button -->
                                <Button x:Name="ApplyOptimizationsButton" 
                                        Content="✨ Apply Selected Optimizations" 
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        FontSize="14"
                                        Margin="0,20,0,0"
                                        HorizontalAlignment="Center"
                                        Click="ApplyOptimizationsButton_Click"/>
                            </StackPanel>
                        </Border>

                        <!-- AI Explanation Panel -->
                        <Border x:Name="AIExplanationPanel" 
                                Style="{StaticResource ModernCardStyle}"
                                Visibility="Collapsed"
                                Margin="0,15,0,0">
                            <StackPanel>
                                <!-- Header -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="🤖 AI Optimization Assistant" 
                                                   FontSize="18" 
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                                        <TextBlock x:Name="AIStatusText" 
                                                   Text="Analyzing your system and preparing optimizations..."
                                                   FontSize="14" 
                                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                                   Margin="0,5,0,0"/>
                                    </StackPanel>

                                    <ui:ProgressRing x:Name="ProgressRing" 
                                                     Grid.Column="1"
                                                     Style="{StaticResource ModernProgressRingStyle}"
                                                     IsActive="True"/>
                                </Grid>
                                
                                <!-- Progress Bar -->
                                <ProgressBar x:Name="DetailedProgressBar" 
                                             Height="8" 
                                             Margin="0,15,0,0"
                                             Background="{DynamicResource SystemControlBackgroundBaseMediumBrush}"
                                             Foreground="{DynamicResource SystemControlHighlightAccentBrush}"/>
                                
                                <TextBlock x:Name="ProgressDetailsText" 
                                           Text="0 / 0 optimizations completed"
                                           FontSize="12" 
                                           Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                           HorizontalAlignment="Center"
                                           Margin="0,5,0,0"/>

                                <!-- AI Explanation Control -->
                                <controls:AIExplanationControl x:Name="AIExplanationControl"
                                                              Margin="0,15,0,0"
                                                              AllowSkip="True"
                                                              TypingSpeed="Medium"/>

                                <!-- Technical Details Section -->
                                <Expander x:Name="TechnicalDetailsExpander" 
                                          Header="🔧 Technical Details" 
                                          Margin="0,10,0,0"
                                          FontWeight="SemiBold"
                                          Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}">
                                    <Border Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                                            CornerRadius="6" 
                                            Padding="15,12" 
                                            Margin="0,5,0,0">
                                        <StackPanel>
                                            <TextBlock x:Name="TechnicalExplanationText" 
                                                       Text="Initializing optimization engine..."
                                                       FontSize="12" 
                                                       Foreground="{DynamicResource SystemControlForegroundBaseMediumHighBrush}"
                                                       TextWrapping="Wrap"
                                                       FontFamily="Consolas"/>
                                            
                                            <!-- Current Operation Details -->
                                            <Border Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                                    CornerRadius="4"
                                                    Padding="10,8"
                                                    Margin="0,10,0,0">
                                                <StackPanel>
                                                    <TextBlock Text="Current Operation:" 
                                                               FontSize="11" 
                                                               FontWeight="SemiBold"
                                                               Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
                                                    <TextBlock x:Name="CurrentOperationText" 
                                                               Text="System initialization"
                                                               FontSize="11" 
                                                               Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"
                                                               FontFamily="Consolas"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Registry Changes Log -->
                                            <TextBlock Text="Registry Changes:" 
                                                       FontSize="11" 
                                                       FontWeight="SemiBold"
                                                       Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                                       Margin="0,10,0,5"/>
                                            <ScrollViewer x:Name="RegistryChangesScrollViewer"
                                                          MaxHeight="80"
                                                          VerticalScrollBarVisibility="Auto"
                                                          Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                                          Padding="10,8">
                                                <TextBlock x:Name="RegistryChangesText" 
                                                           Text="No registry changes yet..."
                                                           FontSize="10" 
                                                           Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                                           FontFamily="Consolas"
                                                           TextWrapping="Wrap"/>
                                            </ScrollViewer>
                                        </StackPanel>
                                    </Border>
                                </Expander>

                                <!-- Cancel Button -->
                                <Button x:Name="CancelOptimizationButton" 
                                        Content="⏹ Cancel Optimization" 
                                        Style="{StaticResource {x:Type Button}}"
                                        FontSize="12"
                                        Padding="15,8"
                                        Margin="0,15,0,0"
                                        HorizontalAlignment="Center"
                                        Click="CancelOptimizationButton_Click"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</Page>
