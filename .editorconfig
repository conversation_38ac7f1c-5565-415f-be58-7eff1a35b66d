root = true

# All files
[*]
charset = utf-8
insert_final_newline = true
trim_trailing_whitespace = true

# C# files
[*.cs]
indent_style = space
indent_size = 4
end_of_line = crlf

# XAML files
[*.xaml]
indent_style = space
indent_size = 2
end_of_line = crlf

# JSON files
[*.json]
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# PowerShell files
[*.ps1]
indent_style = space
indent_size = 4
dotnet_diagnostic.S1067.severity = warning   # Expressions should not be too complex
dotnet_diagnostic.S138.severity = warning    # Functions should not have too many lines of code
dotnet_diagnostic.S1541.severity = warning   # Methods and properties should not be too complex
dotnet_diagnostic.S1200.severity = warning   # Classes should not be coupled to too many other classes

# Security related rules (errors)
dotnet_diagnostic.S2092.severity = error     # Cookies should be secure
dotnet_diagnostic.S4426.severity = error     # Cryptographic keys should be robust
dotnet_diagnostic.S2077.severity = error     # Formatting SQL queries is security-sensitive
dotnet_diagnostic.S3649.severity = error     # User-provided values should be sanitized before use in SQL statements

# XAML files
[*.xaml]
indent_style = space
indent_size = 2
end_of_line = crlf

# JSON files
[*.json]
indent_style = space
indent_size = 2
end_of_line = crlf

# XML files
[*.{xml,csproj,props,targets}]
indent_style = space
indent_size = 2
end_of_line = crlf

# PowerShell files
[*.ps1]
indent_style = space
indent_size = 4
end_of_line = crlf

# Markdown files
[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2
