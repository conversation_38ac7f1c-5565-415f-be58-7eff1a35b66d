<UserControl x:Class="PCOptimizerApp.Controls.AIExplanationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="600">
    
    <UserControl.Resources>
        <!-- Converter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- AI Explanation Style -->
        <Style x:Key="AIExplanationStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundAccentBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Opacity" Value="0.95"/>
        </Style>

        <!-- AI Text Style -->
        <Style x:Key="AITextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="LineHeight" Value="22"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="MinHeight" Value="110"/>
            <Setter Property="MaxWidth" Value="600"/>
        </Style>

        <!-- AI Icon Style -->
        <Style x:Key="AIIconStyle" TargetType="Path">
            <Setter Property="Fill" Value="White"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,2,12,0"/>
        </Style>

        <!-- Typing Cursor Animation -->
        <Storyboard x:Key="CursorBlinkAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="TypingCursor"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.8"/>
        </Storyboard>
    </UserControl.Resources>

    <Border Style="{StaticResource AIExplanationStyle}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- AI Icon -->
            <Path Grid.Column="0" Style="{StaticResource AIIconStyle}">
                <Path.Data>
                    <!-- Brain/AI icon -->
                    M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,9A1,1 0 0,1 11,10A1,1 0 0,1 10,11A1,1 0 0,1 9,10A1,1 0 0,1 10,9M14,9A1,1 0 0,1 15,10A1,1 0 0,1 14,11A1,1 0 0,1 13,10A1,1 0 0,1 14,9M12,14C13.11,14 14,14.45 14,15C14,15.55 13.11,16 12,16C10.89,16 10,15.55 10,15C10,14.45 10.89,14 12,14Z
                </Path.Data>
            </Path>

            <!-- Text Content -->
            <StackPanel Grid.Column="1" Orientation="Vertical">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock x:Name="DisplayTextBlock" 
                              Grid.Column="0"
                              Style="{StaticResource AITextStyle}"/>
                    <TextBlock x:Name="TypingCursor"
                              Grid.Column="1"
                              Text="▌"
                              Style="{StaticResource AITextStyle}"
                              Opacity="1"
                              Margin="2,0,0,0"
                              VerticalAlignment="Top"/>
                </Grid>
            </StackPanel>

            <!-- Skip Hint -->
            <TextBlock Grid.Column="2"
                      Text="Click to skip"
                      FontSize="10"
                      Foreground="White"
                      Opacity="0.7"
                      VerticalAlignment="Bottom"
                      Margin="8,0,0,0"
                      Visibility="{Binding AllowSkip, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
    </Border>

    <UserControl.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource CursorBlinkAnimation}"/>
        </EventTrigger>
    </UserControl.Triggers>
</UserControl>
