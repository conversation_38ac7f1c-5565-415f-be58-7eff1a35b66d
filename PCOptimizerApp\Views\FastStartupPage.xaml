<Page x:Class="PCOptimizerApp.Views.FastStartupPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:ui="http://schemas.modernwpf.com/2019"
      Title="PC Optimizer Pro"
      Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <StackPanel Grid.Row="1" 
                    HorizontalAlignment="Center" 
                    VerticalAlignment="Center"
                    Margin="50">
            
            <!-- App Icon/Logo -->
            <TextBlock Text="⚡" 
                       FontSize="80" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,20"
                       Foreground="{DynamicResource SystemControlForegroundAccentBrush}"/>
            
            <!-- App Title -->
            <TextBlock Text="PC Optimizer Pro" 
                       FontSize="36" 
                       FontWeight="Bold"
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,10"
                       Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
            
            <!-- Subtitle -->
            <TextBlock Text="Advanced Windows PC Optimization Tool" 
                       FontSize="16" 
                       FontStyle="Italic"
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,40"
                       Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>

            <!-- Quick Actions Card -->
            <Border Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"
                    CornerRadius="8"
                    Padding="30"
                    MinWidth="400">
                <StackPanel>
                    <TextBlock Text="Quick Actions" 
                               FontSize="20" 
                               FontWeight="SemiBold"
                               Margin="0,0,0,20"
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Button Name="QuickScanButton"
                                Grid.Row="0" Grid.Column="0"
                                Content="🔍 Quick Scan"
                                Margin="5"
                                Padding="15,10"
                                FontSize="14"
                                Click="QuickScanButton_Click"/>

                        <Button Name="SmartAnalysisButton"
                                Grid.Row="0" Grid.Column="1"
                                Content="🧠 Smart Analysis"
                                Margin="5"
                                Padding="15,10"
                                FontSize="14"
                                Click="SmartAnalysisButton_Click"/>

                        <Button Name="QuickOptimizeButton"
                                Grid.Row="1" Grid.Column="0"
                                Content="⚡ Quick Optimize"
                                Margin="5"
                                Padding="15,10"
                                FontSize="14"
                                Click="QuickOptimizeButton_Click"/>

                        <Button Name="ViewDashboardButton"
                                Grid.Row="1" Grid.Column="1"
                                Content="📊 Dashboard"
                                Margin="5"
                                Padding="15,10"
                                FontSize="14"
                                Click="ViewDashboardButton_Click"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- System Status (will be updated when data loads) -->
            <Border Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"
                    CornerRadius="8"
                    Padding="20"
                    Margin="0,20,0,0"
                    MinWidth="400">
                <StackPanel>
                    <TextBlock Text="System Status" 
                               FontSize="18" 
                               FontWeight="SemiBold"
                               Margin="0,0,0,15"
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="System Health:" Margin="0,5" FontWeight="SemiBold"/>
                        <TextBlock Name="SystemHealthText" Grid.Row="0" Grid.Column="1" Text="Analyzing..." Margin="10,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="CPU Usage:" Margin="0,5" FontWeight="SemiBold"/>
                        <TextBlock Name="CpuUsageText" Grid.Row="1" Grid.Column="1" Text="Loading..." Margin="10,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Memory Usage:" Margin="0,5" FontWeight="SemiBold"/>
                        <TextBlock Name="MemoryUsageText" Grid.Row="2" Grid.Column="1" Text="Loading..." Margin="10,5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Loading Indicator -->
            <StackPanel Name="LoadingIndicator" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Center" 
                        Margin="0,30,0,0">
                <ui:ProgressRing Name="LoadingSpinner" 
                                IsActive="True" 
                                Width="20" 
                                Height="20" 
                                Margin="0,0,10,0"/>
                <TextBlock Name="LoadingText" 
                           Text="Loading system information..." 
                           VerticalAlignment="Center"
                           Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Page>
