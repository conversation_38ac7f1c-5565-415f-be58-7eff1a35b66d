using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;

namespace PCOptimizerApp.Models
{
    /// <summary>
    /// Represents a checklist item with completion status and visual indicators for optimization procedures.
    /// Used in UI to display progress and completion status of various optimization tasks.
    /// Implements ObservableObject for MVVM data binding and real-time UI updates.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: checklist, task progress, UI binding, optimization steps, status tracking
    /// </remarks>
    public class ChecklistItem : ObservableObject
    {
        /// <summary>
        /// The descriptive text explaining what this checklist item represents or accomplishes.
        /// </summary>
        private string _description = "";
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// Indicates whether this checklist item has been completed successfully.
        /// Used for tracking optimization progress and determining overall completion status.
        /// </summary>
        private bool _isCompleted;
        public bool IsCompleted
        {
            get => _isCompleted;
            set => SetProperty(ref _isCompleted, value);
        }

        /// <summary>
        /// Indicates whether this checklist item is currently selected/checked by the user.
        /// Automatically updates the StatusIcon when changed to provide visual feedback.
        /// </summary>
        private bool _isChecked;
        public bool IsChecked
        {
            get => _isChecked;
            set
            {
                SetProperty(ref _isChecked, value);
                // Update visual indicator based on checked state
                StatusIcon = value ? "[✓]" : "[ ]";
            }
        }

        /// <summary>
        /// Visual icon representation of the item's current status (checked/unchecked).
        /// Automatically updated when IsChecked property changes.
        /// </summary>
        private string _statusIcon = "[ ]";
        public string StatusIcon
        {
            get => _statusIcon;
            set => SetProperty(ref _statusIcon, value);
        }

        /// <summary>
        /// Category classification for grouping related checklist items together.
        /// Default category is "SYSTEM" for system-level optimization tasks.
        /// </summary>
        private string _category = "SYSTEM";
        public string Category
        {
            get => _category;
            set => SetProperty(ref _category, value);
        }

        /// <summary>
        /// Textual representation of the current status (e.g., "Pending", "In Progress", "Completed").
        /// Used for displaying detailed status information to users.
        /// </summary>
        private string _statusText = "Pending";
        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }
    }

    /// <summary>
    /// Comprehensive system information model containing hardware specifications, performance metrics, and status data.
    /// This model aggregates all essential system components and their current states for analysis and optimization.
    /// Used by SystemInfoService to provide detailed system overview to users and optimization algorithms.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: system information, hardware specs, performance data, system overview, component details
    /// </remarks>
    public class SystemInfo
    {
        /// <summary>Windows operating system version and edition information</summary>
        public string? OperatingSystem { get; set; }

        /// <summary>CPU model name and brand identification</summary>
        public string? ProcessorName { get; set; }

        /// <summary>Number of CPU cores available for parallel processing</summary>
        public int ProcessorCores { get; set; }

        /// <summary>CPU base clock speed in gigahertz</summary>
        public double ProcessorSpeedGHz { get; set; }

        /// <summary>Total system RAM capacity in gigabytes</summary>
        public long TotalMemoryGB { get; set; }

        /// <summary>Currently available RAM in gigabytes</summary>
        public long AvailableMemoryGB { get; set; }

        /// <summary>Current memory utilization as percentage (0-100)</summary>
        public double MemoryUsagePercentage { get; set; }

        /// <summary>List of all detected storage devices (HDDs, SSDs, NVMe drives)</summary>
        public List<StorageDevice> StorageDevices { get; set; } = new();

        /// <summary>Primary graphics card information and driver status</summary>
        public GraphicsCard? GraphicsCard { get; set; }

        /// <summary>Current CPU temperature in Celsius (if available)</summary>
        public double CpuTemperature { get; set; }

        /// <summary>Current GPU temperature in Celsius (if available)</summary>
        public double GpuTemperature { get; set; }

        /// <summary>Timestamp of when this system information was last collected</summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Represents a storage device (HDD, SSD, NVMe) with capacity, usage, and health information.
    /// Used for storage optimization analysis and disk space management features.
    /// Contains calculated properties for usage percentage and SSD-specific optimization flags.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: storage device, disk information, SSD optimization, drive health, storage capacity
    /// </remarks>
    public class StorageDevice
    {
        /// <summary>Display name of the storage device (e.g., "C:", "System Drive")</summary>
        public string? Name { get; set; }

        /// <summary>Manufacturer model number and brand information</summary>
        public string? Model { get; set; }

        /// <summary>Total storage capacity in gigabytes</summary>
        public long TotalSizeGB { get; set; }

        /// <summary>Available free space in gigabytes</summary>
        public long FreeSpaceGB { get; set; }

        /// <summary>
        /// Calculated storage usage percentage (0-100).
        /// Automatically computed from total size and free space.
        /// </summary>
        public double UsagePercentage => TotalSizeGB > 0 ? (double)(TotalSizeGB - FreeSpaceGB) / TotalSizeGB * 100 : 0;

        /// <summary>Storage technology type (HDD, SSD, NVMe, Unknown)</summary>
        public StorageType Type { get; set; }

        /// <summary>Health status reported by the drive's SMART system</summary>
        public string? Health { get; set; }

        /// <summary>Whether TRIM is enabled for SSD optimization (SSD-specific)</summary>
        public bool TrimEnabled { get; set; }
    }

    /// <summary>
    /// Graphics card information including specifications, driver status, and optimization readiness.
    /// Used for graphics-related optimizations and driver update recommendations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: graphics card, GPU information, driver updates, video memory, graphics optimization
    /// </remarks>
    public class GraphicsCard
    {
        /// <summary>GPU model name and manufacturer (e.g., "NVIDIA GeForce RTX 4080")</summary>
        public string? Name { get; set; }

        /// <summary>Currently installed graphics driver version</summary>
        public string? DriverVersion { get; set; }

        /// <summary>Video memory capacity in megabytes</summary>
        public long VRamMB { get; set; }

        /// <summary>Whether the graphics driver is current and up-to-date</summary>
        public bool DriverUpToDate { get; set; }
    }

    /// <summary>
    /// Enumeration of storage device types for optimization categorization.
    /// Used to apply appropriate optimization strategies based on storage technology.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: storage types, drive technology, optimization categories, SSD vs HDD
    /// </remarks>
    public enum StorageType
    {
        /// <summary>Traditional mechanical hard disk drive</summary>
        HDD,
        /// <summary>Solid-state drive connected via SATA</summary>
        SSD,
        /// <summary>NVMe solid-state drive with PCIe interface</summary>
        NVMe,
        /// <summary>Storage type could not be determined</summary>
        Unknown
    }

    /// <summary>
    /// Real-time performance metrics capturing current system resource utilization and temperatures.
    /// Used by PerformanceMonitoringService for continuous system monitoring and performance analysis.
    /// Timestamped for historical tracking and trend analysis.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: performance metrics, resource monitoring, system utilization, real-time data, performance tracking
    /// </remarks>
    public class PerformanceMetrics
    {
        /// <summary>Current CPU utilization percentage (0-100)</summary>
        public double CpuUsagePercentage { get; set; }

        /// <summary>Current memory utilization percentage (0-100)</summary>
        public double MemoryUsagePercentage { get; set; }

        /// <summary>Current disk I/O utilization percentage (0-100)</summary>
        public double DiskUsagePercentage { get; set; }

        /// <summary>Current disk read/write speed in megabytes per second</summary>
        public double DiskSpeedMBps { get; set; } // Replaced NetworkUsageKbps with disk speed

        /// <summary>Current CPU temperature in Celsius (if available from sensors)</summary>
        public double CpuTemperature { get; set; }

        /// <summary>Current GPU temperature in Celsius (if available from sensors)</summary>
        public double GpuTemperature { get; set; }

        /// <summary>Timestamp when these metrics were captured</summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Comprehensive system health assessment including overall score, status classification, and detailed analysis.
    /// Aggregates multiple health factors into a unified score with actionable recommendations.
    /// Used by SmartAnalysisService to provide users with clear system health overview.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: system health, health score, system analysis, health factors, performance assessment
    /// </remarks>
    public class SystemHealthScore
    {
        /// <summary>Overall system health score (0-100, higher is better)</summary>
        public int OverallScore { get; set; }

        /// <summary>Categorized health status based on overall score</summary>
        public SystemHealthStatus Status { get; set; }

        /// <summary>Individual health factors contributing to the overall score</summary>
        public List<HealthFactor> Factors { get; set; } = new();

        /// <summary>Specific recommendations for improving system health</summary>
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Individual health factor representing a specific aspect of system performance and status.
    /// Used to break down system health into manageable, understandable components.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: health factor, system component health, performance factor, health analysis
    /// </remarks>
    public class HealthFactor
    {
        /// <summary>Name of the health factor (e.g., "Memory Usage", "Disk Health")</summary>
        public string? Name { get; set; }

        /// <summary>Numerical score for this factor (0-100, higher is better)</summary>
        public int Score { get; set; }

        /// <summary>Detailed description of what this factor measures</summary>
        public string? Description { get; set; }

        /// <summary>Impact level of this factor on overall system performance</summary>
        public HealthImpact Impact { get; set; }
    }

    /// <summary>
    /// System health status categories for quick visual assessment.
    /// Used in UI to provide color-coded health indicators.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: health status, system condition, health categories, performance levels
    /// </remarks>
    public enum SystemHealthStatus
    {
        /// <summary>System is performing optimally (90-100 score)</summary>
        Excellent,
        /// <summary>System is performing well with minor issues (70-89 score)</summary>
        Good,
        /// <summary>System has moderate performance issues (50-69 score)</summary>
        Fair,
        /// <summary>System has significant performance problems (25-49 score)</summary>
        Poor,
        /// <summary>System requires immediate attention (0-24 score)</summary>
        Critical
    }

    /// <summary>
    /// Impact level classification for health factors and optimization recommendations.
    /// Used to prioritize optimization efforts and user attention.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: impact level, priority classification, optimization priority, severity levels
    /// </remarks>
    public enum HealthImpact
    {
        /// <summary>Minimal impact on system performance</summary>
        Low,
        /// <summary>Moderate impact on system performance</summary>
        Medium,
        /// <summary>Significant impact on system performance</summary>
        High,
        /// <summary>Critical impact requiring immediate attention</summary>
        Critical
    }

    /// <summary>
    /// Represents a specific optimization action that can be applied to improve system performance.
    /// Contains detailed information about safety, impact, applicability, and reversibility.
    /// Used by OptimizationService to track and manage individual optimization procedures.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization item, system optimization, performance improvement, optimization tracking
    /// </remarks>
    public class OptimizationItem
    {
        /// <summary>Unique identifier for this optimization item</summary>
        public string? Id { get; set; }

        /// <summary>Human-readable name of the optimization</summary>
        public string? Name { get; set; }

        /// <summary>Detailed description of what this optimization does</summary>
        public string? Description { get; set; }

        /// <summary>Category grouping for related optimizations (e.g., "Memory", "Storage", "Network")</summary>
        public string? Category { get; set; }

        /// <summary>Safety rating indicating risk level of applying this optimization</summary>
        public OptimizationSafety Safety { get; set; }

        /// <summary>Expected performance impact level of this optimization</summary>
        public OptimizationImpact Impact { get; set; }

        /// <summary>Whether this optimization is applicable to the current system configuration</summary>
        public bool IsApplicable { get; set; }

        /// <summary>Whether this optimization has already been applied to the system</summary>
        public bool IsApplied { get; set; }

        /// <summary>Whether this optimization can be safely reversed/undone</summary>
        public bool IsReversible { get; set; }

        /// <summary>Description of expected performance improvements</summary>
        public string? ExpectedImprovement { get; set; }

        /// <summary>List of system requirements or conditions needed for this optimization</summary>
        public List<string> Requirements { get; set; } = new();

        /// <summary>Timestamp when this optimization was applied (null if not applied)</summary>
        public DateTime? AppliedDate { get; set; }
    }

    /// <summary>
    /// Safety rating enumeration for optimization procedures.
    /// Numeric values allow for easy comparison and sorting by safety level.
    /// Used to warn users about potentially risky optimizations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization safety, risk level, safety rating, optimization risk assessment
    /// </remarks>
    public enum OptimizationSafety
    {
        /// <summary>Completely safe with no risk of system instability (5)</summary>
        Safe = 5,
        /// <summary>Very low risk with minimal chance of issues (4)</summary>
        MostlySafe = 4,
        /// <summary>Moderate risk requiring user awareness (3)</summary>
        Moderate = 3,
        /// <summary>Higher risk that may cause system issues (2)</summary>
        Risky = 2,
        /// <summary>High risk that could cause serious system problems (1)</summary>
        Dangerous = 1
    }

    /// <summary>
    /// Expected performance impact levels for optimizations.
    /// Used to set user expectations and prioritize optimization efforts.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization impact, performance improvement, impact level, optimization effectiveness
    /// </remarks>
    public enum OptimizationImpact
    {
        /// <summary>Minor performance improvement</summary>
        Low,
        /// <summary>Moderate performance improvement</summary>
        Medium,
        /// <summary>Significant performance improvement</summary>
        High
    }

    /// <summary>
    /// Information about a running process including resource usage and identification details.
    /// Used for process monitoring and startup program management features.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: process information, running processes, resource usage, process monitoring
    /// </remarks>
    public class ProcessInfo
    {
        /// <summary>Process executable name</summary>
        public string? Name { get; set; }

        /// <summary>Unique process identifier (PID)</summary>
        public int ProcessId { get; set; }

        /// <summary>Current CPU usage percentage for this process</summary>
        public double CpuUsagePercentage { get; set; }

        /// <summary>Current memory usage in megabytes</summary>
        public long MemoryUsageMB { get; set; }

        /// <summary>Human-readable description of the process</summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// Represents a program that starts automatically with Windows.
    /// Used by startup management features to optimize boot time and system resource usage.
    /// Contains information about startup location, impact assessment, and control options.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: startup program, boot optimization, startup management, auto-start programs
    /// </remarks>
    public class StartupProgram
    {
        /// <summary>Display name of the startup program</summary>
        public string? Name { get; set; }

        /// <summary>Software publisher/developer name</summary>
        public string? Publisher { get; set; }

        /// <summary>Command line or executable path for the startup program</summary>
        public string? Command { get; set; }

        /// <summary>Where this startup entry is configured (registry, folder, services, etc.)</summary>
        public StartupLocation Location { get; set; }

        /// <summary>Assessed impact on system startup time and performance</summary>
        public StartupImpact Impact { get; set; }

        /// <summary>Whether this startup program is currently enabled</summary>
        public bool IsEnabled { get; set; }

        /// <summary>Whether this startup program can be safely disabled</summary>
        public bool CanDisable { get; set; }

        /// <summary>Description of what this program does</summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// Enumeration of startup program registration locations in Windows.
    /// Used to determine how to modify startup settings for each program.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: startup locations, registry startup, startup management, Windows startup
    /// </remarks>
    public enum StartupLocation
    {
        /// <summary>Registered in Windows Registry startup keys</summary>
        Registry,
        /// <summary>Located in Windows Startup folder</summary>
        StartupFolder,
        /// <summary>Configured as a Windows service</summary>
        Services,
        /// <summary>Configured in Windows Task Scheduler</summary>
        TaskScheduler
    }

    /// <summary>
    /// Impact assessment levels for startup programs on system boot performance.
    /// Used to help users prioritize which startup programs to disable.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: startup impact, boot performance, startup optimization priority
    /// </remarks>
    public enum StartupImpact
    {
        /// <summary>Minimal impact on boot time</summary>
        Low,
        /// <summary>Moderate impact on boot time</summary>
        Medium,
        /// <summary>Significant impact on boot time</summary>
        High
    }

    /// <summary>
    /// Information about system backups created for safety during optimization procedures.
    /// Used by BackupService to track and manage restoration points for system rollback.
    /// Essential for maintaining system safety and recovery capabilities.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: backup information, system backup, restoration points, safety backup, rollback
    /// </remarks>
    public class BackupInfo
    {
        /// <summary>Unique identifier for this backup</summary>
        public string? Id { get; set; }

        /// <summary>Human-readable name for the backup</summary>
        public string? Name { get; set; }

        /// <summary>Timestamp when the backup was created</summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>Type of backup (restore point, registry, settings, full system)</summary>
        public BackupType Type { get; set; }

        /// <summary>Size of the backup in megabytes</summary>
        public long SizeMB { get; set; }

        /// <summary>Description of what this backup contains</summary>
        public string? Description { get; set; }

        /// <summary>List of specific items/settings included in this backup</summary>
        public List<string> BackedUpItems { get; set; } = new();
    }

    /// <summary>
    /// Types of backups that can be created for system safety and recovery.
    /// Used to categorize backups and determine appropriate restoration procedures.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: backup types, backup categories, system restore, backup classification
    /// </remarks>
    public enum BackupType
    {
        /// <summary>Windows System Restore Point</summary>
        SystemRestorePoint,
        /// <summary>Registry backup for specific keys</summary>
        RegistryBackup,
        /// <summary>Application and system settings backup</summary>
        SettingsBackup,
        /// <summary>Complete system image backup</summary>
        FullSystemBackup
    }

    /// <summary>
    /// Event arguments for progress updates during long-running operations.
    /// Used by ProgressTrackingService to provide real-time feedback to users about operation progress.
    /// Contains detailed step information and calculated progress percentage.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: progress tracking, operation progress, progress updates, user feedback, operation status
    /// </remarks>
    public class ProgressUpdateEventArgs : EventArgs
    {
        /// <summary>Unique identifier for the operation being tracked</summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>Human-readable name of the operation</summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>Current step number in the operation sequence</summary>
        public int CurrentStep { get; set; }

        /// <summary>Total number of steps in the operation</summary>
        public int TotalSteps { get; set; }

        /// <summary>Description of the current step being performed</summary>
        public string CurrentStepDescription { get; set; } = string.Empty;

        /// <summary>Additional details about the current operation</summary>
        public string? Details { get; set; }

        /// <summary>
        /// Calculated progress percentage (0-100) based on current and total steps.
        /// Automatically computed from CurrentStep and TotalSteps.
        /// </summary>
        public double ProgressPercentage => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;

        /// <summary>Timestamp when this progress update was generated</summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Historical record of a completed operation including timing, success status, and detailed logs.
    /// Used for operation tracking, debugging, and providing users with operation history.
    /// Contains calculated duration and structured log entries for detailed analysis.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: operation history, operation tracking, audit trail, operation logs, performance history
    /// </remarks>
    public class OperationHistory
    {
        /// <summary>Unique identifier for this operation</summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>Human-readable name of the operation</summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>Timestamp when the operation was started</summary>
        public DateTime StartTime { get; set; }

        /// <summary>Timestamp when the operation completed (null if still running)</summary>
        public DateTime? EndTime { get; set; }

        /// <summary>Whether the operation completed successfully</summary>
        public bool Success { get; set; }

        /// <summary>Result message or output from the operation</summary>
        public string? Result { get; set; }

        /// <summary>Error message if the operation failed</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Calculated duration of the operation.
        /// Returns TimeSpan.Zero if the operation hasn't completed yet.
        /// </summary>
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;

        /// <summary>Detailed log entries generated during the operation</summary>
        public List<OperationLogEntry> LogEntries { get; set; } = new();
    }

    /// <summary>
    /// Individual log entry within an operation's execution history.
    /// Used for detailed debugging and operation analysis with different severity levels.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: operation logs, log entry, debugging logs, operation details, log levels
    /// </remarks>
    public class OperationLogEntry
    {
        /// <summary>Timestamp when this log entry was created</summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>Log level (Info, Warning, Error, Debug)</summary>
        public string Level { get; set; } = string.Empty;

        /// <summary>Log message content</summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>Exception details if this log entry represents an error</summary>
        public string? ExceptionDetails { get; set; }
    }

    // Smart Analysis Models
    /// <summary>
    /// Comprehensive result of smart system analysis containing all detected information and recommendations.
    /// Aggregates system information, performance metrics, hardware detection, usage patterns, and optimization recommendations.
    /// Used by SmartAnalysisService to provide complete system assessment to users.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: smart analysis, system analysis results, comprehensive analysis, system assessment
    /// </remarks>
    public class SmartAnalysisResult
    {
        /// <summary>Unique identifier for this analysis session</summary>
        public string AnalysisId { get; set; } = string.Empty;

        /// <summary>Timestamp when the analysis was started</summary>
        public DateTime StartTime { get; set; }

        /// <summary>Timestamp when the analysis was completed</summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Total duration of the analysis process.
        /// Automatically calculated from start and end times.
        /// </summary>
        public TimeSpan Duration => EndTime.Subtract(StartTime);

        /// <summary>Whether the analysis completed successfully</summary>
        public bool Success { get; set; }

        /// <summary>Error message if the analysis failed</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>Detected system information and specifications</summary>
        public SystemInfo SystemInfo { get; set; } = new();

        /// <summary>Current performance metrics captured during analysis</summary>
        public PerformanceMetrics PerformanceMetrics { get; set; } = new();

        /// <summary>Hardware detection results with optimization enablement</summary>
        public List<HardwareDetectionResult> HardwareDetections { get; set; } = new();

        /// <summary>Detected usage patterns for targeted optimization</summary>
        public List<UsagePatternResult> UsagePatterns { get; set; } = new();

        /// <summary>Generated optimization recommendations based on analysis</summary>
        public List<OptimizationRecommendation> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for smart analysis progress updates with detailed step information.
    /// Used to provide real-time feedback during the analysis process with visual indicators.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: analysis progress, smart analysis tracking, analysis steps, progress feedback
    /// </remarks>
    public class AnalysisProgressEventArgs : EventArgs
    {
        /// <summary>Current step number in the analysis process</summary>
        public int CurrentStep { get; set; }

        /// <summary>Total number of steps in the analysis process</summary>
        public int TotalSteps { get; set; }

        /// <summary>Name of the current analysis step</summary>
        public string StepName { get; set; } = string.Empty;

        /// <summary>Detailed description of what the current step is doing</summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>Icon identifier for visual representation of the current step</summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// Calculated progress percentage (0-100) based on current and total steps.
        /// Automatically computed for progress bar display.
        /// </summary>
        public double ProgressPercentage => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;
    }

    /// <summary>
    /// Result of hardware component detection with optimization enablement information.
    /// Used by HardwareDetectionService to provide detailed hardware analysis and specific optimization options.
    /// Contains component information and list of optimizations enabled for that hardware.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: hardware detection, component detection, hardware optimization, hardware analysis
    /// </remarks>
    public class HardwareDetectionResult
    {
        /// <summary>Type of hardware component (CPU, Memory, Storage, Graphics)</summary>
        public string ComponentType { get; set; } = string.Empty;

        /// <summary>Specific name/model of the detected component</summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>Message describing the detection result and implications</summary>
        public string DetectionMessage { get; set; } = string.Empty;

        /// <summary>Icon identifier for visual representation of the component</summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>List of specific optimizations enabled for this hardware component</summary>
        public List<string> OptimizationsEnabled { get; set; } = new();
    }

    /// <summary>
    /// Result of usage pattern detection for targeted optimization recommendations.
    /// Analyzes user behavior patterns to suggest appropriate optimizations (Gaming, Productivity, Development).
    /// Includes confidence scoring and specific optimization recommendations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: usage patterns, user behavior analysis, targeted optimization, usage detection
    /// </remarks>
    public class UsagePatternResult
    {
        /// <summary>Type of usage pattern detected (Gaming, Productivity, Development)</summary>
        public string PatternType { get; set; } = string.Empty;

        /// <summary>Message describing the detected usage pattern</summary>
        public string DetectionMessage { get; set; } = string.Empty;

        /// <summary>Icon identifier for visual representation of the usage pattern</summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>Confidence level of the pattern detection (0-100)</summary>
        public double Confidence { get; set; }

        /// <summary>List of optimizations recommended for this usage pattern</summary>
        public List<string> RecommendedOptimizations { get; set; } = new();
    }

    /// <summary>
    /// Specific optimization recommendation generated by smart analysis.
    /// Contains detailed information about the optimization including priority, safety, and expected impact.
    /// Used to present prioritized optimization suggestions to users.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization recommendation, smart recommendations, optimization priority, recommended optimizations
    /// </remarks>
    public class OptimizationRecommendation
    {
        /// <summary>Unique identifier for this optimization</summary>
        public string OptimizationId { get; set; } = string.Empty;

        /// <summary>Human-readable name of the optimization</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Detailed description of what this optimization does</summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>Description of expected performance improvements</summary>
        public string ExpectedImprovement { get; set; } = string.Empty;

        /// <summary>Priority ranking (higher numbers = higher priority)</summary>
        public int Priority { get; set; }

        /// <summary>Safety level of this optimization</summary>
        public OptimizationSafety SafetyLevel { get; set; }

        /// <summary>Expected impact level of this optimization</summary>
        public OptimizationImpact ImpactLevel { get; set; }

        /// <summary>Category grouping for this optimization</summary>
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// Expert-level content and analysis for optimization procedures.
    /// Provides detailed technical information, reasoning, and checklists for advanced users.
    /// Used to give users comprehensive understanding of optimization procedures.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: expert content, technical analysis, optimization details, advanced information
    /// </remarks>
    public class OptimizationExpertContent
    {
        /// <summary>Primary reasoning behind the optimization recommendation</summary>
        public string MainReasoning { get; set; } = "";

        /// <summary>Detailed expert analysis of the optimization impact and considerations</summary>
        public string ExpertAnalysis { get; set; } = "";

        /// <summary>Technical implementation details and system-level changes</summary>
        public string TechnicalDetails { get; set; } = "";

        /// <summary>Comprehensive description of expected performance impact</summary>
        public string ExpectedImpact { get; set; } = "";

        /// <summary>Checklist of items to verify before and after optimization</summary>
        public List<string> ChecklistItems { get; set; } = new();
    }

    /// <summary>
    /// Progress tracking item for optimization procedures with detailed status information.
    /// Used to display real-time progress during optimization execution with visual feedback.
    /// Contains progress percentage, status icons, and detailed checklists.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization progress, progress tracking, optimization status, progress display
    /// </remarks>
    public class OptimizationProgressItem
    {
        /// <summary>Name of the optimization being tracked</summary>
        public string? Name { get; set; }

        /// <summary>Detailed description of the current optimization step</summary>
        public string? Description { get; set; }

        /// <summary>Current progress percentage (0-100)</summary>
        public double ProgressPercentage { get; set; }

        /// <summary>Status icon for visual representation of progress</summary>
        public string? StatusIcon { get; set; }

        /// <summary>Detailed checklist of items being processed in this optimization</summary>
        public List<string> ChecklistItems { get; set; } = new();
    }

    // Software Detection Models
    /// <summary>
    /// Represents an installed software application with version and installation details.
    /// Used by SoftwareDetectionService for comprehensive software inventory management.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: installed software, application detection, software inventory, application information
    /// </remarks>
    public class InstalledSoftware
    {
        /// <summary>Display name of the software application</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Software publisher/developer name</summary>
        public string Publisher { get; set; } = string.Empty;

        /// <summary>Installed version of the software</summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>Installation path/directory</summary>
        public string InstallPath { get; set; } = string.Empty;

        /// <summary>Date when the software was installed</summary>
        public DateTime? InstallDate { get; set; }

        /// <summary>Size of the installed software in megabytes</summary>
        public long SizeMB { get; set; }

        /// <summary>Category of software (Browser, Office, Gaming, Development, etc.)</summary>
        public SoftwareCategory Category { get; set; }

        /// <summary>Whether optimization options are available for this software</summary>
        public bool HasOptimizations { get; set; }
    }

    /// <summary>
    /// Browser installation information with configuration paths and optimization readiness.
    /// Used for browser-specific optimizations including cache management and performance tuning.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: browser information, browser detection, browser optimization, web browser
    /// </remarks>
    public class BrowserInfo
    {
        /// <summary>Browser name (Chrome, Firefox, Edge, etc.)</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Installed browser version</summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>Browser executable path</summary>
        public string ExecutablePath { get; set; } = string.Empty;

        /// <summary>User data directory path</summary>
        public string UserDataPath { get; set; } = string.Empty;

        /// <summary>Cache directory path</summary>
        public string CachePath { get; set; } = string.Empty;

        /// <summary>Whether this browser is set as default</summary>
        public bool IsDefault { get; set; }

        /// <summary>Available optimization options for this browser</summary>
        public List<string> AvailableOptimizations { get; set; } = new();
    }

    /// <summary>
    /// Gaming platform information including installed games and optimization options.
    /// Used for gaming-specific optimizations and performance tuning.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: gaming platform, game launcher, gaming optimization, game detection
    /// </remarks>
    public class GamingPlatformInfo
    {
        /// <summary>Platform name (Steam, Epic Games, Origin, etc.)</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Platform version</summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>Platform installation path</summary>
        public string InstallPath { get; set; } = string.Empty;

        /// <summary>Games library path</summary>
        public string LibraryPath { get; set; } = string.Empty;

        /// <summary>List of detected installed games</summary>
        public List<string> InstalledGames { get; set; } = new();

        /// <summary>Available optimization options for this platform</summary>
        public List<string> AvailableOptimizations { get; set; } = new();
    }

    /// <summary>
    /// Development tool information including IDEs and development environments.
    /// Used for development-specific optimizations and tool configuration.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: development tools, IDE detection, development environment, programming tools
    /// </remarks>
    public class DevelopmentToolInfo
    {
        /// <summary>Tool name (Visual Studio, VS Code, IntelliJ, etc.)</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Tool version</summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>Tool installation path</summary>
        public string InstallPath { get; set; } = string.Empty;

        /// <summary>Configuration directory path</summary>
        public string ConfigPath { get; set; } = string.Empty;

        /// <summary>Supported programming languages</summary>
        public List<string> SupportedLanguages { get; set; } = new();

        /// <summary>Available optimization options for this tool</summary>
        public List<string> AvailableOptimizations { get; set; } = new();
    }

    /// <summary>
    /// Office and productivity application information with optimization options.
    /// Used for office-specific optimizations and productivity software tuning.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: office applications, productivity software, office optimization, business applications
    /// </remarks>
    public class OfficeApplicationInfo
    {
        /// <summary>Application name (Microsoft Office, Adobe Creative Suite, etc.)</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>Application version</summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>Application installation path</summary>
        public string InstallPath { get; set; } = string.Empty;

        /// <summary>Application suite (if part of a larger suite)</summary>
        public string Suite { get; set; } = string.Empty;

        /// <summary>Components installed (Word, Excel, Photoshop, etc.)</summary>
        public List<string> InstalledComponents { get; set; } = new();

        /// <summary>Available optimization options for this application</summary>
        public List<string> AvailableOptimizations { get; set; } = new();
    }

    /// <summary>
    /// Software category enumeration for application classification.
    /// Used to group software applications and apply category-specific optimizations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: software categories, application types, software classification
    /// </remarks>
    public enum SoftwareCategory
    {
        /// <summary>Web browsers and browser-related software</summary>
        Browser,
        /// <summary>Office and productivity applications</summary>
        Office,
        /// <summary>Gaming platforms and games</summary>
        Gaming,
        /// <summary>Development tools and IDEs</summary>
        Development,
        /// <summary>System utilities and tools</summary>
        System,
        /// <summary>Media and entertainment software</summary>
        Media,
        /// <summary>Communication and social software</summary>
        Communication,
        /// <summary>Security and antivirus software</summary>
        Security,
        /// <summary>Other miscellaneous software</summary>
        Other
    }

    /// <summary>
    /// Contains information about system memory (RAM) status including total and available memory.
    /// Used for intelligent virtual memory optimization decisions.
    /// </summary>
    public class SystemMemoryInfo
    {
        /// <summary>
        /// Total physical memory (RAM) in gigabytes
        /// </summary>
        public double TotalPhysicalMemoryGB { get; set; }

        /// <summary>
        /// Currently available physical memory in gigabytes
        /// </summary>
        public double AvailablePhysicalMemoryGB { get; set; }
    }

    /// <summary>
    /// Contains information about current virtual memory (page file) configuration.
    /// </summary>
    public class VirtualMemoryInfo
    {
        /// <summary>
        /// Current page file size in megabytes
        /// </summary>
        public int CurrentSizeMB { get; set; }

        /// <summary>
        /// Allocated base size for page file in megabytes
        /// </summary>
        public int AllocatedSizeMB { get; set; }
    }

    /// <summary>
    /// Represents the decision made by the virtual memory optimization analysis.
    /// Contains the recommended action and reasoning for virtual memory management.
    /// </summary>
    public class VirtualMemoryOptimizationDecision
    {
        /// <summary>
        /// The recommended action to take for virtual memory optimization
        /// </summary>
        public VirtualMemoryAction Action { get; set; }

        /// <summary>
        /// Human-readable explanation of why this action was recommended
        /// </summary>
        public string Reason { get; set; } = "";

        /// <summary>
        /// Recommended page file size in megabytes (when applicable)
        /// </summary>
        public int RecommendedSizeMB { get; set; }
    }

    /// <summary>
    /// Enumeration of possible virtual memory optimization actions.
    /// </summary>
    public enum VirtualMemoryAction
    {
        /// <summary>No changes recommended to current virtual memory settings</summary>
        NoChange,
        /// <summary>Completely disable the page file (for very high RAM systems with low usage)</summary>
        DisablePageFile,
        /// <summary>Reduce page file size significantly (for high RAM systems with moderate usage)</summary>
        ReducePageFile,
        /// <summary>Optimize page file size for balanced performance (for high RAM systems with higher usage)</summary>
        OptimizePageFile
    }
}
