using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using System.Collections.ObjectModel;

namespace PCOptimizerApp.ViewModels
{
    public partial class DashboardViewModel : ObservableObject
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly IOptimizationService _optimizationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;

        [ObservableProperty]
        private SystemInfo? _systemInfo;

        [ObservableProperty]
        private PerformanceMetrics? _currentMetrics;

        [ObservableProperty]
        private SystemHealthScore? _healthScore;

        [ObservableProperty]
        private bool _isLoading;

        public ObservableCollection<OptimizationItem> RecommendedOptimizations { get; } = new();
        public ObservableCollection<string> RecentActivities { get; } = new();

        public DashboardViewModel(
            ISystemInfoService systemInfoService,
            IOptimizationService optimizationService,
            IPerformanceMonitoringService performanceMonitoringService)
        {
            _systemInfoService = systemInfoService;
            _optimizationService = optimizationService;
            _performanceMonitoringService = performanceMonitoringService;

            _performanceMonitoringService.PerformanceUpdated += OnPerformanceUpdated;
            
            _ = LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load system information
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                
                // Load performance metrics
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
                
                // Load health score
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                
                // Load recommended optimizations
                var recommendations = await _optimizationService.GetRecommendedOptimizationsAsync();
                RecommendedOptimizations.Clear();
                foreach (var recommendation in recommendations.Take(3)) // Show top 3
                {
                    RecommendedOptimizations.Add(recommendation);
                }

                // Load recent activities
                LoadRecentActivities();
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error initializing dashboard: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OnPerformanceUpdated(object? sender, PerformanceMetrics metrics)
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                CurrentMetrics = metrics;
            });
        }

        private void LoadRecentActivities()
        {
            RecentActivities.Clear();
            RecentActivities.Add("✅ SSD Optimization completed - 15% boot time improvement");
            RecentActivities.Add("🧹 Startup cleanup - 12 programs disabled");
            RecentActivities.Add("💾 Memory optimization - 2.3GB RAM freed");
            RecentActivities.Add("🛡️ Restore point created - Before optimization");
        }

        [RelayCommand]
        private async Task ApplyRecommendationAsync(OptimizationItem optimization)
        {
            try
            {
                if (optimization?.Id != null)
                {
                    var success = await _optimizationService.ApplyOptimizationAsync(optimization.Id);
                    if (success)
                    {
                        optimization.IsApplied = true;
                        RecentActivities.Insert(0, $"✅ {optimization.Name} applied successfully");
                        
                        // Refresh data
                        await LoadDashboardDataAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error updating performance metrics: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task RefreshDashboardAsync()
        {
            await LoadDashboardDataAsync();
        }
    }

    public partial class SystemAnalysisViewModel : ObservableObject
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly IHardwareDetectionService _hardwareDetectionService;

        [ObservableProperty]
        private SystemInfo? _systemInfo;

        [ObservableProperty]
        private SystemHealthScore? _healthScore;

        [ObservableProperty]
        private bool _isAnalyzing;

        public ObservableCollection<ProcessInfo> TopProcesses { get; } = new();
        public ObservableCollection<StartupProgram> StartupPrograms { get; } = new();
        public ObservableCollection<HealthFactor> HealthFactors { get; } = new();

        public SystemAnalysisViewModel(
            ISystemInfoService systemInfoService,
            IHardwareDetectionService hardwareDetectionService)
        {
            _systemInfoService = systemInfoService;
            _hardwareDetectionService = hardwareDetectionService;
        }

        [RelayCommand]
        private async Task RunSystemAnalysisAsync()
        {
            try
            {
                IsAnalyzing = true;

                // Get detailed system information
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                
                // Calculate health score
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                
                // Get top processes
                var processes = await _systemInfoService.GetTopProcessesAsync(10);
                TopProcesses.Clear();
                foreach (var process in processes)
                {
                    TopProcesses.Add(process);
                }

                // Get startup programs
                var startupPrograms = await _systemInfoService.GetStartupProgramsAsync();
                StartupPrograms.Clear();
                foreach (var program in startupPrograms)
                {
                    StartupPrograms.Add(program);
                }

                // Update health factors
                if (HealthScore?.Factors != null)
                {
                    HealthFactors.Clear();
                    foreach (var factor in HealthScore.Factors)
                    {
                        HealthFactors.Add(factor);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error loading health factors: {ex.Message}");
            }
            finally
            {
                IsAnalyzing = false;
            }
        }
    }

    public partial class HardwareViewModel : ObservableObject
    {
        private readonly IHardwareDetectionService _hardwareDetectionService;

        [ObservableProperty]
        private SystemInfo? _systemInfo;

        [ObservableProperty]
        private bool _isDetecting;

        public ObservableCollection<OptimizationItem> HardwareOptimizations { get; } = new();

        public HardwareViewModel(IHardwareDetectionService hardwareDetectionService)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _ = DetectHardwareAsync();
        }

        private async Task DetectHardwareAsync()
        {
            try
            {
                IsDetecting = true;

                SystemInfo = await _hardwareDetectionService.DetectHardwareAsync();
                
                var optimizations = await _hardwareDetectionService.GetHardwareSpecificOptimizationsAsync();
                HardwareOptimizations.Clear();
                foreach (var optimization in optimizations)
                {
                    HardwareOptimizations.Add(optimization);
                }
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error detecting hardware optimizations: {ex.Message}");
            }
            finally
            {
                IsDetecting = false;
            }
        }

        [RelayCommand]
        private async Task RefreshHardwareDetectionAsync()
        {
            await DetectHardwareAsync();
        }
    }

    public partial class SafetyViewModel : ObservableObject
    {
        private readonly IBackupService _backupService;

        [ObservableProperty]
        private bool _isCreatingBackup;

        [ObservableProperty]
        private string _backupStatus = string.Empty;

        public ObservableCollection<BackupInfo> AvailableBackups { get; } = new();

        public SafetyViewModel(IBackupService backupService)
        {
            _backupService = backupService;
            _ = LoadBackupsAsync();
        }

        private async Task LoadBackupsAsync()
        {
            try
            {
                var backups = await _backupService.GetAvailableBackupsAsync();
                AvailableBackups.Clear();
                foreach (var backup in backups)
                {
                    AvailableBackups.Add(backup);
                }
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error loading available backups: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task CreateSystemRestorePointAsync()
        {
            try
            {
                IsCreatingBackup = true;
                BackupStatus = "Creating system restore point...";

                var description = $"Manual restore point - {DateTime.Now:yyyy-MM-dd HH:mm}";
                var success = await _backupService.CreateSystemRestorePointAsync(description);

                if (success)
                {
                    BackupStatus = "System restore point created successfully!";
                    await LoadBackupsAsync();
                }
                else
                {
                    BackupStatus = "Failed to create system restore point.";
                }

                await Task.Delay(3000);
                BackupStatus = string.Empty;
            }
            catch (Exception ex)
            {
                BackupStatus = $"Error: {ex.Message}";
            }
            finally
            {
                IsCreatingBackup = false;
            }
        }

        [RelayCommand]
        private async Task RestoreFromBackupAsync(BackupInfo backup)
        {
            try
            {
                if (backup?.Id != null)
                {
                    BackupStatus = $"Restoring from {backup.Name}...";
                    var success = await _backupService.RestoreFromBackupAsync(backup.Id);
                    
                    if (success)
                    {
                        BackupStatus = "Restore completed successfully!";
                    }
                    else
                    {
                        BackupStatus = "Restore failed.";
                    }

                    await Task.Delay(3000);
                    BackupStatus = string.Empty;
                }
            }
            catch (Exception ex)
            {
                BackupStatus = $"Error: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task DeleteBackupAsync(BackupInfo backup)
        {
            try
            {
                if (backup?.Id != null)
                {
                    var success = await _backupService.DeleteBackupAsync(backup.Id);
                    if (success)
                    {
                        AvailableBackups.Remove(backup);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                System.Diagnostics.Debug.WriteLine($"Error deleting backup: {ex.Message}");
            }
        }
    }
}
