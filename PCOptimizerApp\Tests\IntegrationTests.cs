using Microsoft.Extensions.DependencyInjection;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Diagnostics;

namespace PCOptimizerApp.Tests
{
    /// <summary>
    /// Comprehensive integration tests for the redesigned PC Optimizer Pro
    /// Tests all major functionality including Smart Analysis, Optimization Planning, and Results
    /// </summary>
    public class IntegrationTests
    {
        private readonly ILogger _logger = Log.ForContext<IntegrationTests>();
        private readonly ISystemInfoService _systemInfoService;
        private readonly ISmartAnalysisService _smartAnalysisService;
        private readonly IOptimizationService _optimizationService;
        private readonly IProgressTrackingService _progressTrackingService;

        public IntegrationTests()
        {
            // Initialize services for testing
            _systemInfoService = ServiceLocator.GetService<ISystemInfoService>();
            _smartAnalysisService = ServiceLocator.GetService<ISmartAnalysisService>();
            _optimizationService = ServiceLocator.GetService<IOptimizationService>();
            _progressTrackingService = ServiceLocator.GetService<IProgressTrackingService>();
        }

        /// <summary>
        /// Test 1: Comprehensive System Information Gathering
        /// Validates that all system metrics are accurately collected
        /// </summary>
        public async Task<TestResult> TestSystemInfoGathering()
        {
            var testResult = new TestResult("System Information Gathering");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Starting system information gathering test");

                var systemInfo = await _systemInfoService.GetSystemInfoAsync();

                // Validate required fields
                testResult.AddCheck("CPU Name", !string.IsNullOrEmpty(systemInfo.ProcessorName));
                testResult.AddCheck("CPU Cores", systemInfo.ProcessorCores > 0);
                testResult.AddCheck("Total Memory", systemInfo.TotalMemoryGB > 0);
                testResult.AddCheck("Operating System", !string.IsNullOrEmpty(systemInfo.OperatingSystem));
                testResult.AddCheck("CPU Temperature", systemInfo.CpuTemperature >= 0); // 0 is acceptable fallback

                // Validate storage information
                testResult.AddCheck("Storage Devices", systemInfo.StorageDevices?.Any() == true);
                if (systemInfo.StorageDevices?.Any() == true)
                {
                    var firstDrive = systemInfo.StorageDevices.First();
                    testResult.AddCheck("Drive Model", !string.IsNullOrEmpty(firstDrive.Model));
                    testResult.AddCheck("Drive Type", Enum.IsDefined(typeof(StorageType), firstDrive.Type));
                }

                // Basic validation completed
                testResult.AddCheck("System Info Complete", true);

                stopwatch.Stop();
                testResult.Duration = stopwatch.Elapsed;
                testResult.Success = testResult.Checks.All(c => c.Passed);

                _logger.Information("System info test completed in {Duration}ms. Success: {Success}", 
                    stopwatch.ElapsedMilliseconds, testResult.Success);

                return testResult;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "System information gathering test failed");
                testResult.Success = false;
                testResult.ErrorMessage = ex.Message;
                testResult.Duration = stopwatch.Elapsed;
                return testResult;
            }
        }

        /// <summary>
        /// Test 2: Smart Analysis Engine Validation
        /// Tests hardware detection, usage patterns, and intelligent recommendations
        /// </summary>
        public async Task<TestResult> TestSmartAnalysisEngine()
        {
            var testResult = new TestResult("Smart Analysis Engine");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Starting smart analysis engine test");

                var analysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                // Validate analysis success
                testResult.AddCheck("Analysis Success", analysisResult.Success);
                testResult.AddCheck("Hardware Detections", analysisResult.HardwareDetections?.Any() == true);
                testResult.AddCheck("Usage Patterns", analysisResult.UsagePatterns?.Any() == true);
                testResult.AddCheck("Recommendations", analysisResult.Recommendations?.Any() == true);

                // Validate hardware detection quality
                if (analysisResult.HardwareDetections?.Any() == true)
                {
                    var cpuDetection = analysisResult.HardwareDetections.FirstOrDefault(h => h.ComponentType == "CPU");
                    testResult.AddCheck("CPU Detection", cpuDetection != null);

                    if (cpuDetection != null)
                    {
                        testResult.AddCheck("CPU Optimizations", cpuDetection.OptimizationsEnabled?.Any() == true);
                    }
                }

                // Validate recommendation quality
                if (analysisResult.Recommendations?.Any() == true)
                {
                    var highPriorityRecs = analysisResult.Recommendations.Where(r => r.Priority >= 50).ToList(); // High priority threshold
                    testResult.AddCheck("High Priority Recommendations", highPriorityRecs.Any());
                    
                    foreach (var rec in analysisResult.Recommendations.Take(3))
                    {
                        testResult.AddCheck($"Recommendation '{rec.Name}' has description", !string.IsNullOrEmpty(rec.Description));
                        testResult.AddCheck($"Recommendation '{rec.Name}' has impact", !string.IsNullOrEmpty(rec.ExpectedImprovement));
                    }
                }

                stopwatch.Stop();
                testResult.Duration = stopwatch.Elapsed;
                testResult.Success = testResult.Checks.All(c => c.Passed);

                _logger.Information("Smart analysis test completed in {Duration}ms. Success: {Success}", 
                    stopwatch.ElapsedMilliseconds, testResult.Success);

                return testResult;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Smart analysis engine test failed");
                testResult.Success = false;
                testResult.ErrorMessage = ex.Message;
                testResult.Duration = stopwatch.Elapsed;
                return testResult;
            }
        }

        /// <summary>
        /// Test 3: Optimization Service Functionality
        /// Tests optimization retrieval, application, and tracking
        /// </summary>
        public async Task<TestResult> TestOptimizationService()
        {
            var testResult = new TestResult("Optimization Service");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Starting optimization service test");

                // Test optimization retrieval
                var availableOptimizations = await _optimizationService.GetAvailableOptimizationsAsync();
                testResult.AddCheck("Available Optimizations", availableOptimizations?.Any() == true);

                if (availableOptimizations?.Any() == true)
                {
                    // Test optimization details
                    var firstOpt = availableOptimizations.First();
                    testResult.AddCheck("Optimization Name", !string.IsNullOrEmpty(firstOpt.Name));
                    testResult.AddCheck("Optimization Description", !string.IsNullOrEmpty(firstOpt.Description));
                    testResult.AddCheck("Optimization Category", !string.IsNullOrEmpty(firstOpt.Category));

                    // Test optimization structure
                    var testOptimization = availableOptimizations.FirstOrDefault(o => !o.IsApplied);
                    if (testOptimization != null)
                    {
                        _logger.Information("Testing optimization structure: {Name}", testOptimization.Name);

                        // Note: In a real test, we'd apply and then revert the optimization
                        // For safety, we'll just validate the optimization structure
                        testResult.AddCheck("Unapplied Optimization Available", true);
                    }
                }

                stopwatch.Stop();
                testResult.Duration = stopwatch.Elapsed;
                testResult.Success = testResult.Checks.All(c => c.Passed);

                _logger.Information("Optimization service test completed in {Duration}ms. Success: {Success}", 
                    stopwatch.ElapsedMilliseconds, testResult.Success);

                return testResult;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Optimization service test failed");
                testResult.Success = false;
                testResult.ErrorMessage = ex.Message;
                testResult.Duration = stopwatch.Elapsed;
                return testResult;
            }
        }

        /// <summary>
        /// Test 4: Progress Tracking System
        /// Tests real-time progress updates and event handling
        /// </summary>
        public async Task<TestResult> TestProgressTracking()
        {
            var testResult = new TestResult("Progress Tracking System");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Starting progress tracking test");

                var progressUpdates = new List<ProgressUpdateEventArgs>();
                
                // Subscribe to progress events
                _progressTrackingService.ProgressUpdated += (sender, args) =>
                {
                    progressUpdates.Add(args);
                };

                // Simulate progress tracking
                await _progressTrackingService.UpdateProgressAsync("Test Operation", 0, "Starting test", "Initializing test operation");

                for (int i = 1; i <= 5; i++)
                {
                    var percentage = (i * 100) / 5;
                    await _progressTrackingService.UpdateProgressAsync("Test Operation", percentage, $"Step {i}", $"Processing step {i} of 5");
                    await Task.Delay(100); // Small delay to allow events to fire
                }

                await _progressTrackingService.UpdateProgressAsync("Test Operation", 100, "Test completed", "Test completed successfully");

                // Validate progress tracking
                testResult.AddCheck("Progress Events Fired", progressUpdates.Count > 0);
                testResult.AddCheck("Progress Completion", progressUpdates.Any(p => p.ProgressPercentage >= 100));
                
                if (progressUpdates.Any())
                {
                    var lastUpdate = progressUpdates.Last();
                    testResult.AddCheck("Final Progress 100%", lastUpdate.ProgressPercentage >= 100);
                    testResult.AddCheck("Progress Description", !string.IsNullOrEmpty(lastUpdate.CurrentStepDescription));
                }

                stopwatch.Stop();
                testResult.Duration = stopwatch.Elapsed;
                testResult.Success = testResult.Checks.All(c => c.Passed);

                _logger.Information("Progress tracking test completed in {Duration}ms. Success: {Success}", 
                    stopwatch.ElapsedMilliseconds, testResult.Success);

                return testResult;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Progress tracking test failed");
                testResult.Success = false;
                testResult.ErrorMessage = ex.Message;
                testResult.Duration = stopwatch.Elapsed;
                return testResult;
            }
        }

        /// <summary>
        /// Run all integration tests and return comprehensive results
        /// </summary>
        public async Task<List<TestResult>> RunAllTestsAsync()
        {
            _logger.Information("Starting comprehensive integration test suite");

            var results = new List<TestResult>();

            try
            {
                // Run all tests
                results.Add(await TestSystemInfoGathering());
                results.Add(await TestSmartAnalysisEngine());
                results.Add(await TestOptimizationService());
                results.Add(await TestProgressTracking());

                // Log summary
                var totalTests = results.Count;
                var passedTests = results.Count(r => r.Success);
                var totalChecks = results.Sum(r => r.Checks.Count);
                var passedChecks = results.Sum(r => r.Checks.Count(c => c.Passed));

                _logger.Information("Integration test suite completed. Tests: {Passed}/{Total}, Checks: {PassedChecks}/{TotalChecks}", 
                    passedTests, totalTests, passedChecks, totalChecks);

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Integration test suite failed");
                throw;
            }
        }
    }

    public class TestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public List<TestCheck> Checks { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public string? ErrorMessage { get; set; }

        public TestResult(string testName)
        {
            TestName = testName;
        }

        public void AddCheck(string checkName, bool passed)
        {
            Checks.Add(new TestCheck { Name = checkName, Passed = passed });
        }
    }

    public class TestCheck
    {
        public string Name { get; set; } = string.Empty;
        public bool Passed { get; set; }
    }
}
