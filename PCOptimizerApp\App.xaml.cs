using System.Windows;
using ModernWpf;
using PCOptimizerApp.Views;
using PCOptimizerApp.ViewModels;
using PCOptimizerApp.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Security.Principal;

namespace PCOptimizerApp
{
    /// <summary>
    /// Main WPF application class that handles startup, dependency injection, and application lifecycle.
    /// This class configures the entire PC Optimizer Pro application including:
    /// - Administrator privilege checking and enforcement
    /// - Dependency injection container setup with all services and ViewModels
    /// - Serilog logging configuration with file and console outputs
    /// - ModernWPF theming for Windows 11-style UI
    /// - Service locator initialization for legacy code compatibility
    /// - Graceful application shutdown and resource cleanup
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: startup, dependency injection, logging, admin privileges, theme, application lifecycle
    /// </remarks>
    public partial class App : Application
    {
        /// <summary>
        /// The dependency injection host container that manages all application services and ViewModels.
        /// Used throughout the application lifecycle for service resolution and disposal.
        /// </summary>
        private IHost? _host;

        /// <summary>
        /// Static reference to the service provider for accessing services from anywhere in the application.
        /// </summary>
        public static IServiceProvider? ServiceProvider { get; private set; }

        /// <summary>
        /// Handles application startup by configuring all essential components and services.
        /// This method performs the following critical initialization steps:
        /// 1. Validates administrator privileges (required for system optimization)
        /// 2. Configures Serilog for comprehensive logging (file + console)
        /// 3. Sets up ModernWPF dark theme with custom accent color
        /// 4. Initializes dependency injection container with all services
        /// 5. Registers ViewModels for MVVM pattern implementation
        /// 6. Creates and displays the main application window
        /// </summary>
        /// <param name="e">Startup event arguments containing command line parameters</param>
        /// <remarks>
        /// AI Search Keywords: application startup, service registration, DI container, theme setup, logging config
        /// Critical for understanding application architecture and service dependencies
        /// </remarks>
        protected override void OnStartup(StartupEventArgs e)
        {
            // Check if running with administrator privileges - required for most optimization features
            CheckAdministratorPrivileges();

            // Configure Serilog for structured logging with daily file rotation and console output
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.File("logs/pcoptimizer-.log", rollingInterval: RollingInterval.Day)
                .WriteTo.Console()
                .CreateLogger();

            // Configure Modern WPF theme for Windows 11-style appearance
            ThemeManager.Current.ApplicationTheme = ApplicationTheme.Dark;
            ThemeManager.Current.AccentColor = System.Windows.Media.Color.FromRgb(0, 212, 255);

            // Configure dependency injection container with all application services and ViewModels
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Core system services for optimization functionality
                    services.AddSingleton<IAdminPrivilegeService, AdminPrivilegeService>();
                    services.AddSingleton<ISystemInfoService, SystemInfoService>();
                    services.AddSingleton<IHardwareDetectionService, HardwareDetectionService>();
                    services.AddSingleton<ISoftwareDetectionService, SoftwareDetectionService>();
                    services.AddSingleton<IRegistryService, RegistryService>();
                    services.AddSingleton<IBackupService, BackupService>();
                    services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
                    services.AddSingleton<IOptimizationService, OptimizationService>();
                    services.AddSingleton<IProgressTrackingService, ProgressTrackingService>();
                    services.AddSingleton<ISmartAnalysisService, SmartAnalysisService>();
                    services.AddSingleton<IProgressiveLoadingService, ProgressiveLoadingService>();
                    services.AddSingleton<IAIExplanationService, AIExplanationService>();
                    services.AddSingleton<ISystemAnalysisService, SystemAnalysisService>();

                    // MVVM ViewModels for UI data binding and command handling
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<DashboardViewModel>();
                    services.AddTransient<SystemAnalysisViewModel>();
                    services.AddTransient<OptimizationViewModel>();
                    services.AddTransient<HardwareViewModel>();
                    services.AddTransient<SafetyViewModel>();

                    // WPF Views/Windows for the user interface
                    services.AddTransient<MainWindow>();
                    services.AddTransient<ModernOptimizationPage>();
                })
                .Build();

            // Set the static service provider for global access
            ServiceProvider = _host.Services;

            // Initialize service locator for legacy code compatibility
            ServiceLocator.Initialize(_host.Services);

            // Create and display the main application window
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        /// <summary>
        /// Handles application shutdown by disposing resources and closing log files gracefully.
        /// Ensures proper cleanup of the dependency injection host and Serilog resources
        /// to prevent memory leaks and data loss during application termination.
        /// </summary>
        /// <param name="e">Exit event arguments containing application exit code</param>
        /// <remarks>
        /// AI Search Keywords: application shutdown, resource cleanup, disposal, memory management
        /// </remarks>
        protected override void OnExit(ExitEventArgs e)
        {
            // Dispose of the dependency injection host and all registered services
            _host?.Dispose();
            
            // Ensure all log entries are flushed to disk before application closure
            Log.CloseAndFlush();
            
            base.OnExit(e);
        }

        /// <summary>
        /// Validates that the application is running with administrator privileges and handles user consent.
        /// PC Optimizer Pro requires elevated permissions for most optimization features including:
        /// - Registry modifications for system optimization
        /// - Windows service management for startup optimization
        /// - System restore point creation for safety
        /// - Power plan modifications for performance tuning
        /// - System file cleanup and disk optimization
        /// 
        /// If admin privileges are not detected, presents user with detailed explanation
        /// and option to continue with limited functionality or exit the application.
        /// </summary>
        /// <remarks>
        /// AI Search Keywords: administrator privileges, UAC, elevated permissions, system security, user consent
        /// Critical method for ensuring application has necessary permissions for system optimization
        /// </remarks>
        private void CheckAdministratorPrivileges()
        {
            try
            {
                // Check current user's Windows identity and role membership
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                bool isElevated = principal.IsInRole(WindowsBuiltInRole.Administrator);

                if (!isElevated)
                {
                    // Present detailed explanation of why admin privileges are required
                    var result = MessageBox.Show(
                        "PC Optimizer Pro requires administrator privileges to function properly.\n\n" +
                        "Many optimization features will not work without elevated permissions:\n" +
                        "• Registry modifications\n" +
                        "• System service management\n" +
                        "• System restore point creation\n" +
                        "• Power plan modifications\n" +
                        "• System file cleanup\n\n" +
                        "Please restart the application as an administrator.\n\n" +
                        "Do you want to continue anyway? (Limited functionality)",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    // Allow user to exit if they prefer not to continue with limitations
                    if (result == MessageBoxResult.No)
                    {
                        this.Shutdown();
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log any errors in privilege checking but don't block application startup
                Log.Warning(ex, "Failed to check administrator privileges");
            }
        }

        /// <summary>
        /// Static utility method to check if the current application instance is running with administrator privileges.
        /// This method provides a simple boolean check that can be used throughout the application
        /// to conditionally enable/disable features that require elevated permissions.
        /// </summary>
        /// <returns>
        /// True if the application is running with administrator privileges, false otherwise.
        /// Returns false if any exception occurs during the privilege check.
        /// </returns>
        /// <remarks>
        /// AI Search Keywords: administrator check, privilege validation, UAC status, elevation check
        /// Used by services and ViewModels to determine feature availability based on permissions
        /// </remarks>
        public static bool IsRunningAsAdministrator()
        {
            try
            {
                // Query current Windows identity and check administrator role membership
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                // Return false for any exceptions to err on the side of caution
                return false;
            }
        }
    }
}
