using System;
using PCOptimizerApp.Controls;

namespace PCOptimizerApp.Models
{
    /// <summary>
    /// AI-generated explanation for an optimization with personalized context
    /// </summary>
    public class OptimizationExplanation
    {
        public string OptimizationId { get; set; } = string.Empty;
        public string PreAnalysisText { get; set; } = string.Empty;
        public string AnalysisText { get; set; } = string.Empty;
        public string ActionText { get; set; } = string.Empty;
        public string CompletionText { get; set; } = string.Empty;
        public TypingSpeedMode TypingSpeed { get; set; } = TypingSpeedMode.Medium;
        public TimeSpan ExpectedDuration { get; set; } = TimeSpan.FromSeconds(5);
    }

    /// <summary>
    /// System analysis context for personalizing AI explanations
    /// </summary>
    public class SystemAnalysisContext
    {
        public int TotalRamGB { get; set; }
        public int CpuCores { get; set; }
        public string WindowsVersion { get; set; } = string.Empty;
        public bool HasSsd { get; set; }
        public int TempFilesCount { get; set; }
        public double TempFilesSizeGB { get; set; }
        public int StartupProgramsCount { get; set; }
        public int CurrentBootTimeSeconds { get; set; }
        public int EstimatedBootTimeSeconds { get; set; }
        public int EstimatedPerformanceGain { get; set; }
        public string[] DetectedSoftware { get; set; } = Array.Empty<string>();
        public string GpuInfo { get; set; } = string.Empty;
        public double AvailableStorageGB { get; set; }
        public double UsedStoragePercent { get; set; }
    }
}
