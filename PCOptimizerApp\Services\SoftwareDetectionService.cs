using Microsoft.Win32;
using PCOptimizerApp.Models;
using Serilog;
using System.Management;
using System.IO;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service for detecting installed software applications and providing software-specific optimization recommendations.
    /// Implements comprehensive software detection using registry scanning, file system analysis, and WMI queries.
    /// Essential for enabling application-aware optimizations and targeted performance improvements.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: software detection, application detection, software optimization, application-specific tuning
    /// </remarks>
    public class SoftwareDetectionService : ISoftwareDetectionService
    {
        private readonly ILogger _logger = Log.ForContext<SoftwareDetectionService>();
        private readonly IRegistryService _registryService;

        public SoftwareDetectionService(IRegistryService registryService)
        {
            _registryService = registryService;
        }

        /// <summary>
        /// Detects all installed software applications on the system using multiple detection methods.
        /// Scans Windows registry, Program Files directories, and WMI for comprehensive software inventory.
        /// </summary>
        /// <returns>List of detected software applications with version and installation details</returns>
        public async Task<List<InstalledSoftware>> DetectInstalledSoftwareAsync()
        {
            try
            {
                _logger.Information("Starting comprehensive software detection");
                var installedSoftware = new List<InstalledSoftware>();

                // Detect from registry uninstall keys
                var registrySoftware = await DetectFromRegistryAsync();
                installedSoftware.AddRange(registrySoftware);

                // Detect from WMI Win32_Product
                var wmiSoftware = await DetectFromWmiAsync();
                installedSoftware.AddRange(wmiSoftware);

                // Remove duplicates and categorize
                var uniqueSoftware = RemoveDuplicates(installedSoftware);
                CategorizeAndFlagOptimizations(uniqueSoftware);

                _logger.Information("Software detection completed. Found {Count} applications", uniqueSoftware.Count);
                return uniqueSoftware;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to detect installed software");
                return new List<InstalledSoftware>();
            }
        }

        /// <summary>
        /// Checks if a specific software application is installed on the system.
        /// Uses multiple detection methods for accurate software presence verification.
        /// </summary>
        /// <param name="softwareName">Name or identifier of the software to check</param>
        /// <returns>True if the software is detected as installed, false otherwise</returns>
        public async Task<bool> IsSoftwareInstalledAsync(string softwareName)
        {
            try
            {
                var allSoftware = await DetectInstalledSoftwareAsync();
                return allSoftware.Any(s => s.Name.Contains(softwareName, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to check if software {SoftwareName} is installed", softwareName);
                return false;
            }
        }

        /// <summary>
        /// Retrieves optimizations applicable to detected software applications.
        /// Provides software-specific optimization recommendations based on installed applications.
        /// </summary>
        /// <returns>List of software-specific optimizations available for current software configuration</returns>
        public async Task<List<OptimizationItem>> GetSoftwareSpecificOptimizationsAsync()
        {
            try
            {
                _logger.Information("Generating software-specific optimizations");
                var optimizations = new List<OptimizationItem>();
                var installedSoftware = await DetectInstalledSoftwareAsync();

                // Add optimizations based on detected software
                foreach (var software in installedSoftware.Where(s => s.HasOptimizations))
                {
                    optimizations.AddRange(GetOptimizationsForSoftware(software));
                }

                _logger.Information("Generated {Count} software-specific optimizations", optimizations.Count);
                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to get software-specific optimizations");
                return new List<OptimizationItem>();
            }
        }

        /// <summary>
        /// Detects browser installations and configurations for browser-specific optimizations.
        /// Identifies Chrome, Firefox, Edge, and other browsers with their data directories.
        /// </summary>
        /// <returns>List of detected browsers with configuration paths and optimization options</returns>
        public async Task<List<BrowserInfo>> DetectInstalledBrowsersAsync()
        {
            try
            {
                _logger.Information("Detecting installed browsers");
                var browsers = new List<BrowserInfo>();

                // Chrome detection
                await DetectChrome(browsers);
                
                // Firefox detection
                await DetectFirefox(browsers);
                
                // Edge detection
                await DetectEdge(browsers);

                _logger.Information("Browser detection completed. Found {Count} browsers", browsers.Count);
                return browsers;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to detect installed browsers");
                return new List<BrowserInfo>();
            }
        }

        /// <summary>
        /// Detects gaming platforms and installed games for gaming-specific optimizations.
        /// Identifies Steam, Epic Games, Origin, and other gaming platforms.
        /// </summary>
        /// <returns>List of detected gaming platforms and installed games</returns>
        public async Task<List<GamingPlatformInfo>> DetectGamingPlatformsAsync()
        {
            try
            {
                _logger.Information("Detecting gaming platforms");
                var platforms = new List<GamingPlatformInfo>();

                // Steam detection
                await DetectSteam(platforms);
                
                // Epic Games detection
                await DetectEpicGames(platforms);
                
                // Origin detection
                await DetectOrigin(platforms);

                _logger.Information("Gaming platform detection completed. Found {Count} platforms", platforms.Count);
                return platforms;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to detect gaming platforms");
                return new List<GamingPlatformInfo>();
            }
        }

        /// <summary>
        /// Detects development tools and IDEs for development-specific optimizations.
        /// Identifies Visual Studio, VS Code, IntelliJ, and other development environments.
        /// </summary>
        /// <returns>List of detected development tools and their configuration paths</returns>
        public async Task<List<DevelopmentToolInfo>> DetectDevelopmentToolsAsync()
        {
            try
            {
                _logger.Information("Detecting development tools");
                var tools = new List<DevelopmentToolInfo>();

                // Visual Studio detection
                await DetectVisualStudio(tools);
                
                // VS Code detection
                await DetectVSCode(tools);
                
                // IntelliJ/JetBrains detection
                await DetectJetBrains(tools);

                _logger.Information("Development tools detection completed. Found {Count} tools", tools.Count);
                return tools;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to detect development tools");
                return new List<DevelopmentToolInfo>();
            }
        }

        /// <summary>
        /// Detects office and productivity applications for office-specific optimizations.
        /// Identifies Microsoft Office, Adobe Creative Suite, and other productivity software.
        /// </summary>
        /// <returns>List of detected office and productivity applications</returns>
        public async Task<List<OfficeApplicationInfo>> DetectOfficeApplicationsAsync()
        {
            try
            {
                _logger.Information("Detecting office applications");
                var applications = new List<OfficeApplicationInfo>();

                // Microsoft Office detection
                await DetectMicrosoftOffice(applications);
                
                // Adobe Creative Suite detection
                await DetectAdobeCreativeSuite(applications);
                
                // LibreOffice detection
                await DetectLibreOffice(applications);

                _logger.Information("Office applications detection completed. Found {Count} applications", applications.Count);
                return applications;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to detect office applications");
                return new List<OfficeApplicationInfo>();
            }
        }

        #region Private Detection Methods

        private async Task<List<InstalledSoftware>> DetectFromRegistryAsync()
        {
            var software = new List<InstalledSoftware>();
            var uninstallKeys = new[]
            {
                @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                @"HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            };

            foreach (var keyPath in uninstallKeys)
            {
                await Task.Run(() =>
                {
                    try
                    {
                        using var key = Registry.LocalMachine.OpenSubKey(keyPath.Replace("HKEY_LOCAL_MACHINE\\", ""));
                        if (key == null) return;

                        foreach (var subKeyName in key.GetSubKeyNames())
                        {
                            using var subKey = key.OpenSubKey(subKeyName);
                            if (subKey == null) continue;

                            var displayName = subKey.GetValue("DisplayName")?.ToString();
                            if (string.IsNullOrWhiteSpace(displayName)) continue;

                            software.Add(new InstalledSoftware
                            {
                                Name = displayName,
                                Publisher = subKey.GetValue("Publisher")?.ToString() ?? "",
                                Version = subKey.GetValue("DisplayVersion")?.ToString() ?? "",
                                InstallPath = subKey.GetValue("InstallLocation")?.ToString() ?? "",
                                InstallDate = ParseInstallDate(subKey.GetValue("InstallDate")?.ToString()),
                                SizeMB = ParseSize(subKey.GetValue("EstimatedSize")?.ToString())
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to read registry key {KeyPath}", keyPath);
                    }
                });
            }

            return software;
        }

        private async Task<List<InstalledSoftware>> DetectFromWmiAsync()
        {
            var software = new List<InstalledSoftware>();

            await Task.Run(() =>
            {
                try
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Product");
                    using var collection = searcher.Get();

                    foreach (ManagementObject obj in collection)
                    {
                        var name = obj["Name"]?.ToString();
                        if (string.IsNullOrWhiteSpace(name)) continue;

                        software.Add(new InstalledSoftware
                        {
                            Name = name,
                            Publisher = obj["Vendor"]?.ToString() ?? "",
                            Version = obj["Version"]?.ToString() ?? "",
                            InstallPath = obj["InstallLocation"]?.ToString() ?? "",
                            InstallDate = ParseInstallDate(obj["InstallDate"]?.ToString())
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to query WMI for installed software");
                }
            });

            return software;
        }

        private static List<InstalledSoftware> RemoveDuplicates(List<InstalledSoftware> software)
        {
            return software
                .GroupBy(s => s.Name.ToLowerInvariant())
                .Select(g => g.First())
                .ToList();
        }

        private void CategorizeAndFlagOptimizations(List<InstalledSoftware> software)
        {
            foreach (var app in software)
            {
                app.Category = DetermineCategory(app.Name);
                app.HasOptimizations = HasKnownOptimizations(app.Name);
            }
        }

        private static SoftwareCategory DetermineCategory(string softwareName)
        {
            var name = softwareName.ToLowerInvariant();
            
            if (name.Contains("chrome") || name.Contains("firefox") || name.Contains("edge") || name.Contains("browser"))
                return SoftwareCategory.Browser;
            
            if (name.Contains("office") || name.Contains("word") || name.Contains("excel") || name.Contains("powerpoint"))
                return SoftwareCategory.Office;
            
            if (name.Contains("steam") || name.Contains("epic") || name.Contains("origin") || name.Contains("game"))
                return SoftwareCategory.Gaming;
            
            if (name.Contains("visual studio") || name.Contains("code") || name.Contains("intellij") || name.Contains("eclipse"))
                return SoftwareCategory.Development;
            
            return SoftwareCategory.Other;
        }

        private static bool HasKnownOptimizations(string softwareName)
        {
            var name = softwareName.ToLowerInvariant();
            var knownOptimizations = new[]
            {
                "chrome", "firefox", "edge", "office", "word", "excel", "powerpoint",
                "steam", "epic", "origin", "visual studio", "code", "photoshop", "premiere"
            };
            
            return knownOptimizations.Any(opt => name.Contains(opt));
        }

        private List<OptimizationItem> GetOptimizationsForSoftware(InstalledSoftware software)
        {
            var optimizations = new List<OptimizationItem>();
            var name = software.Name.ToLowerInvariant();

            // Add specific optimizations based on software type
            if (name.Contains("chrome"))
            {
                optimizations.Add(CreateOptimization("chrome_cache", "Chrome Cache Optimization", "Browser"));
                optimizations.Add(CreateOptimization("chrome_memory", "Chrome Memory Management", "Browser"));
            }
            
            if (name.Contains("office"))
            {
                optimizations.Add(CreateOptimization("office_startup", "Office Startup Optimization", "Office"));
                optimizations.Add(CreateOptimization("office_animations", "Office Animation Disable", "Office"));
            }

            return optimizations;
        }

        private static OptimizationItem CreateOptimization(string id, string name, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Category = category,
                Description = $"Optimizes {name} for better performance",
                Safety = OptimizationSafety.Safe,
                Impact = OptimizationImpact.Medium,
                IsApplicable = true,
                IsReversible = true
            };
        }

        private static Task DetectChrome(List<BrowserInfo> browsers)
        {
            var chromePaths = new[]
            {
                @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            };

            foreach (var path in chromePaths)
            {
                if (File.Exists(path))
                {
                    var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Google", "Chrome", "User Data");
                    browsers.Add(new BrowserInfo
                    {
                        Name = "Google Chrome",
                        ExecutablePath = path,
                        UserDataPath = userDataPath,
                        CachePath = Path.Combine(userDataPath, "Default", "Cache"),
                        AvailableOptimizations = { "Cache Cleanup", "Memory Optimization", "Startup Optimization" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectFirefox(List<BrowserInfo> browsers)
        {
            var firefoxPaths = new[]
            {
                @"C:\Program Files\Mozilla Firefox\firefox.exe",
                @"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
            };

            foreach (var path in firefoxPaths)
            {
                if (File.Exists(path))
                {
                    var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Mozilla", "Firefox");
                    browsers.Add(new BrowserInfo
                    {
                        Name = "Mozilla Firefox",
                        ExecutablePath = path,
                        UserDataPath = userDataPath,
                        CachePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Mozilla", "Firefox", "Profiles"),
                        AvailableOptimizations = { "Cache Cleanup", "Profile Optimization", "Performance Tuning" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectEdge(List<BrowserInfo> browsers)
        {
            var edgePath = @"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe";
            if (File.Exists(edgePath))
            {
                var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Microsoft", "Edge", "User Data");
                browsers.Add(new BrowserInfo
                {
                    Name = "Microsoft Edge",
                    ExecutablePath = edgePath,
                    UserDataPath = userDataPath,
                    CachePath = Path.Combine(userDataPath, "Default", "Cache"),
                    AvailableOptimizations = { "Cache Cleanup", "Memory Optimization", "Privacy Optimization" }
                });
            }
            return Task.CompletedTask;
        }

        private static Task DetectSteam(List<GamingPlatformInfo> platforms)
        {
            var steamPaths = new[]
            {
                @"C:\Program Files\Steam\steam.exe",
                @"C:\Program Files (x86)\Steam\steam.exe"
            };

            foreach (var path in steamPaths)
            {
                if (File.Exists(path))
                {
                    var installPath = Path.GetDirectoryName(path) ?? "";
                    platforms.Add(new GamingPlatformInfo
                    {
                        Name = "Steam",
                        InstallPath = installPath,
                        LibraryPath = Path.Combine(installPath, "steamapps"),
                        AvailableOptimizations = { "Cache Cleanup", "Shader Cache Optimization", "Download Optimization" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectEpicGames(List<GamingPlatformInfo> platforms)
        {
            var epicPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Epic Games", "Launcher", "Portal", "Binaries", "Win32", "EpicGamesLauncher.exe");
            if (File.Exists(epicPath))
            {
                platforms.Add(new GamingPlatformInfo
                {
                    Name = "Epic Games Launcher",
                    InstallPath = Path.GetDirectoryName(epicPath) ?? "",
                    AvailableOptimizations = { "Cache Cleanup", "Launcher Optimization" }
                });
            }
            return Task.CompletedTask;
        }

        private static Task DetectOrigin(List<GamingPlatformInfo> platforms)
        {
            var originPaths = new[]
            {
                @"C:\Program Files\Origin\Origin.exe",
                @"C:\Program Files (x86)\Origin\Origin.exe"
            };

            foreach (var path in originPaths)
            {
                if (File.Exists(path))
                {
                    platforms.Add(new GamingPlatformInfo
                    {
                        Name = "Origin",
                        InstallPath = Path.GetDirectoryName(path) ?? "",
                        AvailableOptimizations = { "Cache Cleanup", "Background Process Optimization" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectVisualStudio(List<DevelopmentToolInfo> tools)
        {
            // Detection logic for Visual Studio
            var vsPaths = Directory.GetDirectories(@"C:\Program Files\Microsoft Visual Studio", "*", SearchOption.TopDirectoryOnly)
                .Where(d => Directory.Exists(d))
                .ToArray();

            foreach (var vsPath in vsPaths)
            {
                var devenvPath = Path.Combine(vsPath, "Common7", "IDE", "devenv.exe");
                if (File.Exists(devenvPath))
                {
                    tools.Add(new DevelopmentToolInfo
                    {
                        Name = $"Visual Studio {Path.GetFileName(vsPath)}",
                        InstallPath = vsPath,
                        ConfigPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Microsoft", "VisualStudio"),
                        SupportedLanguages = { "C#", "C++", "VB.NET", "F#", "JavaScript", "TypeScript" },
                        AvailableOptimizations = { "IntelliSense Optimization", "Build Performance", "Extension Management" }
                    });
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectVSCode(List<DevelopmentToolInfo> tools)
        {
            var vscodePackages = new[]
            {
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "Microsoft VS Code", "Code.exe"),
                @"C:\Program Files\Microsoft VS Code\Code.exe"
            };

            foreach (var path in vscodePackages)
            {
                if (File.Exists(path))
                {
                    tools.Add(new DevelopmentToolInfo
                    {
                        Name = "Visual Studio Code",
                        InstallPath = Path.GetDirectoryName(path) ?? "",
                        ConfigPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Code"),
                        SupportedLanguages = { "JavaScript", "TypeScript", "Python", "C#", "Java", "Go" },
                        AvailableOptimizations = { "Extension Optimization", "Settings Optimization", "Workspace Performance" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectJetBrains(List<DevelopmentToolInfo> tools)
        {
            var jetbrainsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "JetBrains", "Toolbox");
            if (Directory.Exists(jetbrainsPath))
            {
                tools.Add(new DevelopmentToolInfo
                {
                    Name = "JetBrains Toolbox",
                    InstallPath = jetbrainsPath,
                    ConfigPath = jetbrainsPath,
                    SupportedLanguages = { "Java", "Kotlin", "Python", "JavaScript", "PHP", "C#" },
                    AvailableOptimizations = { "JVM Optimization", "Index Optimization", "Plugin Management" }
                });
            }
            return Task.CompletedTask;
        }

        private static Task DetectMicrosoftOffice(List<OfficeApplicationInfo> applications)
        {
            var officePaths = new[]
            {
                @"C:\Program Files\Microsoft Office",
                @"C:\Program Files (x86)\Microsoft Office"
            };

            foreach (var officePath in officePaths)
            {
                if (Directory.Exists(officePath))
                {
                    var components = new List<string>();
                    var officeRoot = Directory.GetDirectories(officePath, "root", SearchOption.AllDirectories).FirstOrDefault();
                    
                    if (officeRoot != null)
                    {
                        var binPath = Path.Combine(officeRoot, "Office16");
                        if (Directory.Exists(binPath))
                        {
                            if (File.Exists(Path.Combine(binPath, "WINWORD.EXE"))) components.Add("Word");
                            if (File.Exists(Path.Combine(binPath, "EXCEL.EXE"))) components.Add("Excel");
                            if (File.Exists(Path.Combine(binPath, "POWERPNT.EXE"))) components.Add("PowerPoint");
                            if (File.Exists(Path.Combine(binPath, "OUTLOOK.EXE"))) components.Add("Outlook");
                        }
                    }

                    if (components.Any())
                    {
                        applications.Add(new OfficeApplicationInfo
                        {
                            Name = "Microsoft Office",
                            Suite = "Microsoft Office",
                            InstallPath = officePath,
                            InstalledComponents = components,
                            AvailableOptimizations = { "Startup Optimization", "Animation Disable", "Add-in Management", "Cache Cleanup" }
                        });
                    }
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectAdobeCreativeSuite(List<OfficeApplicationInfo> applications)
        {
            var adobePath = @"C:\Program Files\Adobe";
            if (Directory.Exists(adobePath))
            {
                var components = new List<string>();
                var subdirs = Directory.GetDirectories(adobePath);
                
                foreach (var dir in subdirs)
                {
                    var dirName = Path.GetFileName(dir).ToLowerInvariant();
                    if (dirName.Contains("photoshop")) components.Add("Photoshop");
                    if (dirName.Contains("premiere")) components.Add("Premiere Pro");
                    if (dirName.Contains("after effects")) components.Add("After Effects");
                    if (dirName.Contains("illustrator")) components.Add("Illustrator");
                }

                if (components.Any())
                {
                    applications.Add(new OfficeApplicationInfo
                    {
                        Name = "Adobe Creative Suite",
                        Suite = "Adobe Creative Suite",
                        InstallPath = adobePath,
                        InstalledComponents = components,
                        AvailableOptimizations = { "Cache Management", "Performance Optimization", "GPU Acceleration" }
                    });
                }
            }
            return Task.CompletedTask;
        }

        private static Task DetectLibreOffice(List<OfficeApplicationInfo> applications)
        {
            var librePaths = new[]
            {
                @"C:\Program Files\LibreOffice",
                @"C:\Program Files (x86)\LibreOffice"
            };

            foreach (var path in librePaths)
            {
                if (Directory.Exists(path))
                {
                    applications.Add(new OfficeApplicationInfo
                    {
                        Name = "LibreOffice",
                        Suite = "LibreOffice",
                        InstallPath = path,
                        InstalledComponents = { "Writer", "Calc", "Impress", "Draw" },
                        AvailableOptimizations = { "Startup Optimization", "Memory Management", "Java Runtime Optimization" }
                    });
                    break;
                }
            }
            return Task.CompletedTask;
        }

        private static DateTime? ParseInstallDate(string? dateString)
        {
            if (string.IsNullOrWhiteSpace(dateString)) return null;
            
            // Try parsing YYYYMMDD format
            if (dateString.Length == 8 && DateTime.TryParseExact(dateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var date))
                return date;
            
            // Try standard DateTime parsing
            if (DateTime.TryParse(dateString, out var parsedDate))
                return parsedDate;
            
            return null;
        }

        private static long ParseSize(string? sizeString)
        {
            if (string.IsNullOrWhiteSpace(sizeString)) return 0;
            
            if (long.TryParse(sizeString, out var size))
                return size / 1024; // Convert KB to MB
            
            return 0;
        }

        #endregion
    }
}
