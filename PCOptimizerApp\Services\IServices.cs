using PCOptimizerApp.Models;
using System.Collections.ObjectModel;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Interface for system information gathering and hardware analysis services.
    /// Provides comprehensive system data collection including hardware specs, performance metrics,
    /// health assessment, process monitoring, and startup program analysis.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: system information, hardware detection, performance monitoring, system health, process analysis
    /// Implemented by SystemInfoService for complete system analysis and monitoring
    /// </remarks>
    public interface ISystemInfoService
    {
        /// <summary>
        /// Retrieves comprehensive system information including hardware specifications and current status.
        /// Gathers CPU, memory, storage, graphics card details and system configuration.
        /// </summary>
        /// <returns>Complete system information model with hardware and software details</returns>
        Task<SystemInfo> GetSystemInfoAsync();

        /// <summary>
        /// Captures current real-time performance metrics including CPU, memory, and disk usage.
        /// Provides instant snapshot of system resource utilization for monitoring purposes.
        /// </summary>
        /// <returns>Current performance metrics with resource utilization percentages</returns>
        Task<PerformanceMetrics> GetCurrentPerformanceMetricsAsync();

        /// <summary>
        /// Calculates comprehensive system health score based on multiple performance factors.
        /// Analyzes various system aspects to provide overall health assessment with recommendations.
        /// </summary>
        /// <returns>System health score with categorized status and improvement recommendations</returns>
        Task<SystemHealthScore> CalculateSystemHealthScoreAsync();

        /// <summary>
        /// Retrieves list of top resource-consuming processes for system analysis.
        /// Identifies processes with highest CPU and memory usage for optimization targeting.
        /// </summary>
        /// <param name="count">Maximum number of top processes to return (default: 10)</param>
        /// <returns>List of process information sorted by resource consumption</returns>
        Task<List<ProcessInfo>> GetTopProcessesAsync(int count = 10);

        /// <summary>
        /// Retrieves list of all startup programs with impact analysis and control options.
        /// Analyzes programs that start with Windows for boot optimization opportunities.
        /// </summary>
        /// <returns>List of startup programs with impact assessment and management options</returns>
        Task<List<StartupProgram>> GetStartupProgramsAsync();
    }

    /// <summary>
    /// Interface for system optimization services providing performance enhancement capabilities.
    /// Manages optimization discovery, application, reversal, and batch processing.
    /// Includes both manual optimization selection and automated quick optimization features.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization service, performance optimization, system tuning, optimization management
    /// Implemented by OptimizationService for comprehensive system performance enhancement
    /// </remarks>
    public interface IOptimizationService
    {
        /// <summary>
        /// Retrieves all available optimizations that can be applied to the current system.
        /// Includes safety ratings, impact assessments, and applicability analysis.
        /// </summary>
        /// <returns>Complete list of available optimizations with detailed information</returns>
        Task<List<OptimizationItem>> GetAvailableOptimizationsAsync();

        /// <summary>
        /// Retrieves optimizations recommended specifically for the current system configuration.
        /// Uses intelligent analysis to suggest most beneficial optimizations for the user's setup.
        /// </summary>
        /// <returns>Curated list of recommended optimizations based on system analysis</returns>
        Task<List<OptimizationItem>> GetRecommendedOptimizationsAsync();

        /// <summary>
        /// Applies a specific optimization to the system with safety checks and backup creation.
        /// Creates restoration points and validates system requirements before application.
        /// </summary>
        /// <param name="optimizationId">Unique identifier of the optimization to apply</param>
        /// <returns>True if optimization was applied successfully, false otherwise</returns>
        Task<bool> ApplyOptimizationAsync(string optimizationId);

        /// <summary>
        /// Reverts a previously applied optimization using backup restoration.
        /// Safely restores system state to before the optimization was applied.
        /// </summary>
        /// <param name="optimizationId">Unique identifier of the optimization to revert</param>
        /// <returns>True if optimization was reverted successfully, false otherwise</returns>
        Task<bool> RevertOptimizationAsync(string optimizationId);

        /// <summary>
        /// Applies multiple optimizations as a batch operation with comprehensive progress tracking.
        /// Optimizes execution order and handles dependencies between optimizations.
        /// </summary>
        /// <param name="optimizationIds">List of optimization identifiers to apply</param>
        /// <returns>True if all optimizations were applied successfully, false if any failed</returns>
        Task<bool> ApplyMultipleOptimizationsAsync(List<string> optimizationIds);

        /// <summary>
        /// Performs automated quick optimization using intelligently selected safe optimizations.
        /// Provides rapid performance improvement with minimal user interaction and maximum safety.
        /// </summary>
        /// <returns>Detailed results of the quick optimization process including applied changes</returns>
        Task<OptimizationResult> RunQuickOptimizeAsync();

        /// <summary>
        /// Gets all possible optimizations regardless of software installation status.
        /// This includes browser, office, gaming, and development optimizations even if the software is not installed.
        /// The applicability is checked but all optimizations are returned for UI display.
        /// </summary>
        /// <returns>Complete list of all optimizations for UI display</returns>
        Task<List<OptimizationItem>> GetAllOptimizationsAsync();

    }

    /// <summary>
    /// Interface for hardware detection and hardware-specific optimization services.
    /// Analyzes system hardware components to enable targeted optimization strategies.
    /// Provides hardware-aware optimization recommendations and compatibility checking.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: hardware detection, hardware optimization, component analysis, hardware-specific tuning
    /// Implemented by HardwareDetectionService for intelligent hardware-based optimization
    /// </remarks>
    public interface IHardwareDetectionService
    {
        /// <summary>
        /// Performs comprehensive hardware detection and analysis of system components.
        /// Identifies CPU, memory, storage, and graphics hardware for optimization targeting.
        /// </summary>
        /// <returns>Detailed system information with detected hardware specifications</returns>
        Task<SystemInfo> DetectHardwareAsync();

        /// <summary>
        /// Retrieves optimizations specifically applicable to the detected hardware configuration.
        /// Provides hardware-targeted optimizations for maximum performance improvement.
        /// </summary>
        /// <returns>List of optimizations tailored to current hardware setup</returns>
        Task<List<OptimizationItem>> GetHardwareSpecificOptimizationsAsync();

        /// <summary>
        /// Checks if SSD-specific optimizations are applicable to the current storage configuration.
        /// Determines presence of solid-state drives for TRIM and SSD optimization enablement.
        /// </summary>
        /// <returns>True if SSD optimizations can be applied, false otherwise</returns>
        Task<bool> IsSsdOptimizationApplicableAsync();

        /// <summary>
        /// Checks if gaming mode optimizations are applicable based on hardware capabilities.
        /// Analyzes system specifications to determine gaming optimization suitability.
        /// </summary>
        /// <returns>True if gaming optimizations are beneficial for this system, false otherwise</returns>
        Task<bool> IsGameModeOptimizationApplicableAsync();
    }

    /// <summary>
    /// Interface for comprehensive backup and restoration services ensuring system safety.
    /// Provides multiple backup types including system restore points, registry backups, and full system images.
    /// Essential for safe optimization with rollback capabilities and disaster recovery.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: backup service, system restore, rollback, safety backup, disaster recovery
    /// Implemented by BackupService for comprehensive system safety and recovery capabilities
    /// </remarks>
    public interface IBackupService
    {
        /// <summary>
        /// Creates a Windows System Restore Point for comprehensive system rollback capability.
        /// Provides system-wide restoration point before major optimizations.
        /// </summary>
        /// <param name="description">Descriptive name for the restore point</param>
        /// <returns>True if restore point was created successfully, false otherwise</returns>
        Task<bool> CreateSystemRestorePointAsync(string description);

        /// <summary>
        /// Creates a backup of specific registry keys for targeted rollback capability.
        /// Enables precise restoration of registry modifications made during optimization.
        /// </summary>
        /// <param name="registryPath">Registry path to backup (e.g., "HKEY_LOCAL_MACHINE\SOFTWARE\...")</param>
        /// <returns>True if registry backup was created successfully, false otherwise</returns>
        Task<bool> CreateRegistryBackupAsync(string registryPath);

        /// <summary>
        /// Creates a backup of important system and application settings.
        /// Preserves user preferences and system configuration for restoration.
        /// </summary>
        /// <returns>True if settings backup was created successfully, false otherwise</returns>
        Task<bool> CreateSettingsBackupAsync();

        /// <summary>
        /// Retrieves list of all available backups with detailed information.
        /// Provides comprehensive backup inventory for user selection and management.
        /// </summary>
        /// <returns>List of available backups with metadata and restoration options</returns>
        Task<List<BackupInfo>> GetAvailableBackupsAsync();

        /// <summary>
        /// Restores system state from a previously created backup.
        /// Provides complete rollback capability for system recovery.
        /// </summary>
        /// <param name="backupId">Unique identifier of the backup to restore</param>
        /// <returns>True if restoration was successful, false otherwise</returns>
        Task<bool> RestoreFromBackupAsync(string backupId);

        /// <summary>
        /// Deletes a specific backup to free up storage space.
        /// Removes backup files and metadata from the system.
        /// </summary>
        /// <param name="backupId">Unique identifier of the backup to delete</param>
        /// <returns>True if backup was deleted successfully, false otherwise</returns>
        Task<bool> DeleteBackupAsync(string backupId);

        // Enhanced backup and rollback capabilities
        /// <summary>
        /// Creates a specialized backup for a specific optimization with affected registry keys.
        /// Provides targeted rollback capability for individual optimization procedures.
        /// </summary>
        /// <param name="optimizationName">Name of the optimization being backed up</param>
        /// <param name="affectedRegistryKeys">List of registry keys that will be modified</param>
        /// <returns>Backup ID for the created optimization backup</returns>
        Task<string> CreateOptimizationBackupAsync(string optimizationName, List<string> affectedRegistryKeys);

        /// <summary>
        /// Rolls back a specific optimization using its associated backup.
        /// Restores system state to before the optimization was applied.
        /// </summary>
        /// <param name="backupId">Unique identifier of the optimization backup</param>
        /// <returns>True if rollback was successful, false otherwise</returns>
        Task<bool> RollbackOptimizationAsync(string backupId);

        /// <summary>
        /// Creates a comprehensive full system backup for complete disaster recovery.
        /// Provides maximum safety for major system modifications.
        /// </summary>
        /// <param name="description">Descriptive name for the full system backup</param>
        /// <returns>True if full system backup was created successfully, false otherwise</returns>
        Task<bool> CreateFullSystemBackupAsync(string description);

        /// <summary>
        /// Retrieves list of available Windows System Restore Points.
        /// Provides access to system-created and user-created restore points.
        /// </summary>
        /// <returns>List of available system restore points with creation dates and descriptions</returns>
        Task<List<BackupInfo>> GetSystemRestorePointsAsync();

        /// <summary>
        /// Validates the integrity and completeness of a backup before restoration.
        /// Ensures backup is valid and can be successfully restored.
        /// </summary>
        /// <param name="backupId">Unique identifier of the backup to validate</param>
        /// <returns>True if backup is valid and restorable, false otherwise</returns>
        Task<bool> ValidateBackupIntegrityAsync(string backupId);

        /// <summary>
        /// Retrieves detailed information about a specific backup.
        /// Provides metadata, size, creation date, and contents information.
        /// </summary>
        /// <param name="backupId">Unique identifier of the backup</param>
        /// <returns>Detailed backup information or null if backup not found</returns>
        Task<BackupInfo?> GetBackupInfoAsync(string backupId);
    }

    /// <summary>
    /// Interface for Windows Registry manipulation services with safety and backup integration.
    /// Provides secure registry operations with comprehensive error handling and validation.
    /// Essential for system optimizations that require registry modifications.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: registry service, registry manipulation, registry backup, Windows registry
    /// Implemented by RegistryService for safe registry operations and system configuration
    /// </remarks>
    public interface IRegistryService
    {
        /// <summary>
        /// Sets a registry value with automatic backup and error handling.
        /// Creates backup before modification and validates write permissions.
        /// </summary>
        /// <param name="keyPath">Full registry key path (e.g., "HKEY_LOCAL_MACHINE\SOFTWARE\...")</param>
        /// <param name="valueName">Name of the registry value to set</param>
        /// <param name="value">Value to write (supports string, DWORD, binary, etc.)</param>
        /// <returns>True if registry value was set successfully, false otherwise</returns>
        Task<bool> SetRegistryValueAsync(string keyPath, string valueName, object value);

        /// <summary>
        /// Retrieves a registry value with type safety and error handling.
        /// Handles missing keys and values gracefully without throwing exceptions.
        /// </summary>
        /// <param name="keyPath">Full registry key path to read from</param>
        /// <param name="valueName">Name of the registry value to retrieve</param>
        /// <returns>Registry value object or null if key/value not found</returns>
        Task<object?> GetRegistryValueAsync(string keyPath, string valueName);

        /// <summary>
        /// Deletes a specific registry value with safety checks and backup creation.
        /// Validates permissions and creates backup before deletion.
        /// </summary>
        /// <param name="keyPath">Full registry key path containing the value</param>
        /// <param name="valueName">Name of the registry value to delete</param>
        /// <returns>True if registry value was deleted successfully, false otherwise</returns>
        Task<bool> DeleteRegistryValueAsync(string keyPath, string valueName);

        /// <summary>
        /// Creates a new registry key with proper permissions and error handling.
        /// Creates parent keys as needed and validates write permissions.
        /// </summary>
        /// <param name="keyPath">Full path of the registry key to create</param>
        /// <returns>True if registry key was created successfully, false otherwise</returns>
        Task<bool> CreateRegistryKeyAsync(string keyPath);

        /// <summary>
        /// Deletes an entire registry key and all its subkeys and values.
        /// Creates comprehensive backup before deletion for safety.
        /// </summary>
        /// <param name="keyPath">Full path of the registry key to delete</param>
        /// <returns>True if registry key was deleted successfully, false otherwise</returns>
        Task<bool> DeleteRegistryKeyAsync(string keyPath);

        /// <summary>
        /// Creates a backup of a registry key and all its subkeys and values.
        /// Exports registry data to a file for restoration purposes.
        /// </summary>
        /// <param name="keyPath">Full path of the registry key to backup</param>
        /// <param name="backupPath">File path where the backup should be saved</param>
        /// <returns>True if registry backup was created successfully, false otherwise</returns>
        Task<bool> BackupRegistryKeyAsync(string keyPath, string backupPath);

        /// <summary>
        /// Restores a registry key from a previously created backup file.
        /// Imports registry data and overwrites existing keys and values.
        /// </summary>
        /// <param name="backupPath">File path of the backup to restore</param>
        /// <param name="keyPath">Target registry key path for restoration</param>
        /// <returns>True if registry restoration was successful, false otherwise</returns>
        Task<bool> RestoreRegistryKeyAsync(string backupPath, string keyPath);
    }

    /// <summary>
    /// Interface for real-time performance monitoring and historical data collection.
    /// Provides continuous system resource monitoring with event-driven updates and historical analysis.
    /// Essential for performance tracking and optimization impact assessment.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: performance monitoring, real-time monitoring, system metrics, performance tracking
    /// Implemented by PerformanceMonitoringService for continuous system performance observation
    /// </remarks>
    public interface IPerformanceMonitoringService
    {
        /// <summary>
        /// Event raised when new performance metrics are available.
        /// Provides real-time updates to UI components and monitoring systems.
        /// </summary>
        event EventHandler<PerformanceMetrics>? PerformanceUpdated;

        /// <summary>
        /// Starts continuous performance monitoring with configurable update intervals.
        /// Begins background collection of CPU, memory, disk, and temperature metrics.
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Stops performance monitoring and releases system resources.
        /// Cleanly shuts down monitoring threads and performance counters.
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// Retrieves current performance metrics snapshot without starting continuous monitoring.
        /// Provides instant performance data for on-demand analysis.
        /// </summary>
        /// <returns>Current system performance metrics including resource utilization</returns>
        Task<PerformanceMetrics> GetCurrentMetricsAsync();

        /// <summary>
        /// Retrieves historical performance metrics for trend analysis and reporting.
        /// Enables performance tracking over time and optimization impact assessment.
        /// </summary>
        /// <param name="from">Start date for historical data retrieval</param>
        /// <param name="to">End date for historical data retrieval</param>
        /// <returns>List of performance metrics within the specified time range</returns>
        Task<List<PerformanceMetrics>> GetHistoricalMetricsAsync(DateTime from, DateTime to);
    }

    /// <summary>
    /// Interface for operation progress tracking and historical operation management.
    /// Provides comprehensive progress reporting, operation logging, and history tracking.
    /// Essential for user feedback during long-running optimization operations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: progress tracking, operation progress, progress reporting, operation history
    /// Implemented by ProgressTrackingService for comprehensive operation monitoring and reporting
    /// </remarks>
    public interface IProgressTrackingService
    {
        /// <summary>
        /// Event raised when operation progress is updated.
        /// Provides real-time progress information to UI components and progress bars.
        /// </summary>
        event EventHandler<ProgressUpdateEventArgs>? ProgressUpdated;

        /// <summary>
        /// Initializes tracking for a new operation with step count and identification.
        /// Sets up progress monitoring infrastructure for the specified operation.
        /// </summary>
        /// <param name="operationId">Unique identifier for the operation</param>
        /// <param name="operationName">Human-readable name of the operation</param>
        /// <param name="totalSteps">Total number of steps in the operation</param>
        /// <returns>Task representing the asynchronous operation setup</returns>
        Task StartOperationAsync(string operationId, string operationName, int totalSteps);

        /// <summary>
        /// Updates progress for an ongoing operation with current step and description.
        /// Triggers progress events and updates UI with current operation status.
        /// </summary>
        /// <param name="operationId">Unique identifier of the operation to update</param>
        /// <param name="currentStep">Current step number in the operation sequence</param>
        /// <param name="currentStepDescription">Description of the current step being performed</param>
        /// <param name="details">Optional additional details about the current step</param>
        /// <returns>Task representing the asynchronous progress update</returns>
        Task UpdateProgressAsync(string operationId, int currentStep, string currentStepDescription, string? details = null);

        /// <summary>
        /// Completes an operation and records final results in operation history.
        /// Finalizes operation tracking and creates historical record for audit purposes.
        /// </summary>
        /// <param name="operationId">Unique identifier of the operation to complete</param>
        /// <param name="success">Whether the operation completed successfully</param>
        /// <param name="result">Optional result message or output from the operation</param>
        /// <returns>Task representing the asynchronous operation completion</returns>
        Task CompleteOperationAsync(string operationId, bool success, string? result = null);

        /// <summary>
        /// Retrieves historical record of all completed operations for audit and analysis.
        /// Provides operation history for troubleshooting and performance analysis.
        /// </summary>
        /// <returns>List of operation history records with timing and results</returns>
        Task<List<OperationHistory>> GetOperationHistoryAsync();

        /// <summary>
        /// Logs detailed information about an operation for debugging and audit purposes.
        /// Creates structured log entries within the operation's execution context.
        /// </summary>
        /// <param name="operationId">Unique identifier of the operation</param>
        /// <param name="level">Log level (Info, Warning, Error, Debug)</param>
        /// <param name="message">Log message content</param>
        /// <param name="exception">Optional exception details for error logs</param>
        /// <returns>Task representing the asynchronous logging operation</returns>
        Task LogOperationDetailAsync(string operationId, string level, string message, Exception? exception = null);
    }

    /// <summary>
    /// Interface for intelligent system analysis and smart optimization recommendation services.
    /// Provides comprehensive system assessment using advanced analysis algorithms to generate
    /// hardware-aware, usage-pattern-based optimization recommendations with progress tracking.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: smart analysis, intelligent optimization, usage patterns, hardware analysis, smart recommendations
    /// Implemented by SmartAnalysisService for advanced system analysis and intelligent optimization suggestions
    /// </remarks>
    public interface ISmartAnalysisService
    {
        /// <summary>
        /// Event raised during smart analysis to provide real-time progress updates.
        /// Enables UI components to display analysis progress with detailed step information.
        /// </summary>
        event EventHandler<AnalysisProgressEventArgs>? AnalysisProgressUpdated;

        /// <summary>
        /// Performs comprehensive smart analysis of the system including hardware detection,
        /// usage pattern analysis, and intelligent optimization recommendation generation.
        /// Provides complete system assessment with actionable insights.
        /// </summary>
        /// <returns>Complete smart analysis results with recommendations and detected patterns</returns>
        Task<SmartAnalysisResult> PerformSmartAnalysisAsync();

        /// <summary>
        /// Detects and analyzes hardware capabilities to enable hardware-specific optimizations.
        /// Identifies system components and their optimization potential for targeted improvements.
        /// </summary>
        /// <returns>List of hardware detection results with enabled optimizations</returns>
        Task<List<HardwareDetectionResult>> DetectHardwareCapabilitiesAsync();

        /// <summary>
        /// Analyzes system usage patterns to provide targeted optimization recommendations.
        /// Identifies user behavior patterns (Gaming, Productivity, Development) for smart optimization targeting.
        /// </summary>
        /// <returns>List of detected usage patterns with confidence scores and recommendations</returns>
        Task<List<UsagePatternResult>> AnalyzeUsagePatternsAsync();

        /// <summary>
        /// Generates intelligent optimization recommendations based on comprehensive system analysis.
        /// Uses system information, hardware capabilities, and usage patterns to suggest optimal improvements.
        /// </summary>
        /// <param name="systemInfo">Current system information for analysis</param>
        /// <returns>Prioritized list of optimization recommendations with safety and impact assessment</returns>
        Task<List<OptimizationRecommendation>> GenerateSmartRecommendationsAsync(SystemInfo systemInfo);
    }

    /// <summary>
    /// Interface for software detection and software-specific optimization services.
    /// Detects installed applications and provides software-aware optimization recommendations.
    /// Essential for applying optimizations tailored to specific software configurations.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: software detection, application detection, software optimization, application-specific tuning
    /// Implemented by SoftwareDetectionService for intelligent software-based optimization
    /// </remarks>
    public interface ISoftwareDetectionService
    {
        /// <summary>
        /// Detects all installed software applications on the system.
        /// Scans registry, program files, and other locations to build comprehensive software inventory.
        /// </summary>
        /// <returns>List of detected software applications with version and installation details</returns>
        Task<List<InstalledSoftware>> DetectInstalledSoftwareAsync();

        /// <summary>
        /// Checks if a specific software application is installed on the system.
        /// Uses multiple detection methods for accurate software presence verification.
        /// </summary>
        /// <param name="softwareName">Name or identifier of the software to check</param>
        /// <returns>True if the software is detected as installed, false otherwise</returns>
        Task<bool> IsSoftwareInstalledAsync(string softwareName);

        /// <summary>
        /// Retrieves optimizations applicable to detected software applications.
        /// Provides software-specific optimization recommendations based on installed applications.
        /// </summary>
        /// <returns>List of software-specific optimizations available for current software configuration</returns>
        Task<List<OptimizationItem>> GetSoftwareSpecificOptimizationsAsync();

        /// <summary>
        /// Detects browser installations and configurations for browser-specific optimizations.
        /// Identifies Chrome, Firefox, Edge, and other browsers with their data directories.
        /// </summary>
        /// <returns>List of detected browsers with configuration paths and optimization options</returns>
        Task<List<BrowserInfo>> DetectInstalledBrowsersAsync();

        /// <summary>
        /// Detects gaming platforms and installed games for gaming-specific optimizations.
        /// Identifies Steam, Epic Games, Origin, and other gaming platforms.
        /// </summary>
        /// <returns>List of detected gaming platforms and installed games</returns>
        Task<List<GamingPlatformInfo>> DetectGamingPlatformsAsync();

        /// <summary>
        /// Detects development tools and IDEs for development-specific optimizations.
        /// Identifies Visual Studio, VS Code, IntelliJ, and other development environments.
        /// </summary>
        /// <returns>List of detected development tools and their configuration paths</returns>
        Task<List<DevelopmentToolInfo>> DetectDevelopmentToolsAsync();

        /// <summary>
        /// Detects office and productivity applications for office-specific optimizations.
        /// Identifies Microsoft Office, Adobe Creative Suite, and other productivity software.
        /// </summary>
        /// <returns>List of detected office and productivity applications</returns>
        Task<List<OfficeApplicationInfo>> DetectOfficeApplicationsAsync();
    }

    /// <summary>
    /// Result model for optimization operations containing detailed execution information.
    /// Provides comprehensive feedback about optimization execution including success status,
    /// applied changes, failures, and performance impact assessment.
    /// </summary>
    /// <remarks>
    /// AI Search Keywords: optimization result, optimization feedback, operation results, performance impact
    /// </remarks>
    public class OptimizationResult
    {
        /// <summary>Overall success status of the optimization operation</summary>
        public bool Success { get; set; }

        /// <summary>List of optimizations that were successfully applied</summary>
        public List<string> AppliedOptimizations { get; set; } = new();

        /// <summary>List of optimizations that failed to apply</summary>
        public List<string> FailedOptimizations { get; set; } = new();

        /// <summary>Error message if the operation failed</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>Total time taken to complete the optimization operation</summary>
        public TimeSpan Duration { get; set; }

        /// <summary>Estimated performance improvement percentage (0-100)</summary>
        public int ImprovementPercentage { get; set; }
    }
}
