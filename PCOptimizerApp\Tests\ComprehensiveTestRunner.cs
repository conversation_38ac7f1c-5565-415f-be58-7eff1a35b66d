using Serilog;
using System.Diagnostics;
using System.Text;

namespace PCOptimizerApp.Tests
{
    /// <summary>
    /// Comprehensive test runner for PC Optimizer Pro redesign validation
    /// Runs all integration tests and UX validations to ensure quality
    /// </summary>
    public class ComprehensiveTestRunner
    {
        private readonly ILogger _logger = Log.ForContext<ComprehensiveTestRunner>();
        private readonly IntegrationTests _integrationTests;
        private readonly UserExperienceValidator _uxValidator;

        public ComprehensiveTestRunner()
        {
            _integrationTests = new IntegrationTests();
            _uxValidator = new UserExperienceValidator();
        }

        /// <summary>
        /// Run all tests and generate comprehensive report
        /// </summary>
        public async Task<TestSuiteResult> RunCompleteTestSuiteAsync()
        {
            var suiteResult = new TestSuiteResult();
            var overallStopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("🚀 Starting comprehensive PC Optimizer Pro test suite");

                // Phase 1: Integration Tests
                _logger.Information("📋 Phase 1: Running Integration Tests");
                var integrationResults = await _integrationTests.RunAllTestsAsync();
                suiteResult.IntegrationResults = integrationResults;

                // Phase 2: UX Validation Tests
                _logger.Information("🎨 Phase 2: Running UX Validation Tests");
                var uxResults = await _uxValidator.RunAllValidationsAsync();
                suiteResult.UXResults = uxResults;

                // Calculate overall results
                overallStopwatch.Stop();
                suiteResult.TotalDuration = overallStopwatch.Elapsed;
                suiteResult.OverallSuccess = integrationResults.All(r => r.Success) && uxResults.All(r => r.Success);

                // Generate detailed report
                var report = GenerateDetailedReport(suiteResult);
                suiteResult.DetailedReport = report;

                _logger.Information("✅ Test suite completed in {Duration}ms. Overall Success: {Success}", 
                    overallStopwatch.ElapsedMilliseconds, suiteResult.OverallSuccess);

                return suiteResult;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "❌ Test suite failed");
                suiteResult.OverallSuccess = false;
                suiteResult.ErrorMessage = ex.Message;
                suiteResult.TotalDuration = overallStopwatch.Elapsed;
                return suiteResult;
            }
        }

        /// <summary>
        /// Generate a detailed test report
        /// </summary>
        private static string GenerateDetailedReport(TestSuiteResult suiteResult)
        {
            var report = new StringBuilder();

            report.AppendLine("🎉 PC OPTIMIZER PRO - COMPREHENSIVE TEST REPORT");
            report.AppendLine(new string('=', 60));
            report.AppendLine($"📅 Test Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"⏱️ Total Duration: {suiteResult.TotalDuration.TotalSeconds:F2} seconds");
            report.AppendLine($"✅ Overall Success: {(suiteResult.OverallSuccess ? "PASSED" : "FAILED")}");
            report.AppendLine();

            // Integration Test Results
            report.AppendLine("📋 INTEGRATION TEST RESULTS");
            report.AppendLine(new string('-', 40));
            
            foreach (var test in suiteResult.IntegrationResults)
            {
                var status = test.Success ? "✅ PASS" : "❌ FAIL";
                report.AppendLine($"{status} {test.TestName} ({test.Duration.TotalMilliseconds:F0}ms)");
                
                foreach (var check in test.Checks)
                {
                    var checkStatus = check.Passed ? "  ✓" : "  ✗";
                    report.AppendLine($"{checkStatus} {check.Name}");
                }
                
                if (!string.IsNullOrEmpty(test.ErrorMessage))
                {
                    report.AppendLine($"  ⚠️ Error: {test.ErrorMessage}");
                }
                report.AppendLine();
            }

            // UX Validation Results
            report.AppendLine("🎨 USER EXPERIENCE VALIDATION RESULTS");
            report.AppendLine(new string('-', 40));
            
            foreach (var test in suiteResult.UXResults)
            {
                var status = test.Success ? "✅ PASS" : "❌ FAIL";
                report.AppendLine($"{status} {test.TestName} ({test.Duration.TotalMilliseconds:F0}ms)");
                
                foreach (var validation in test.Validations)
                {
                    var validationStatus = validation.Passed ? "  ✓" : "  ✗";
                    report.AppendLine($"{validationStatus} {validation.Name}");
                    if (!string.IsNullOrEmpty(validation.Details))
                    {
                        report.AppendLine($"    📝 {validation.Details}");
                    }
                }
                
                if (!string.IsNullOrEmpty(test.ErrorMessage))
                {
                    report.AppendLine($"  ⚠️ Error: {test.ErrorMessage}");
                }
                report.AppendLine();
            }

            // Summary Statistics
            report.AppendLine("📊 SUMMARY STATISTICS");
            report.AppendLine(new string('-', 40));
            
            var totalIntegrationTests = suiteResult.IntegrationResults.Count;
            var passedIntegrationTests = suiteResult.IntegrationResults.Count(r => r.Success);
            var totalIntegrationChecks = suiteResult.IntegrationResults.Sum(r => r.Checks.Count);
            var passedIntegrationChecks = suiteResult.IntegrationResults.Sum(r => r.Checks.Count(c => c.Passed));

            var totalUXTests = suiteResult.UXResults.Count;
            var passedUXTests = suiteResult.UXResults.Count(r => r.Success);
            var totalUXValidations = suiteResult.UXResults.Sum(r => r.Validations.Count);
            var passedUXValidations = suiteResult.UXResults.Sum(r => r.Validations.Count(v => v.Passed));

            report.AppendLine($"Integration Tests: {passedIntegrationTests}/{totalIntegrationTests} passed");
            report.AppendLine($"Integration Checks: {passedIntegrationChecks}/{totalIntegrationChecks} passed");
            report.AppendLine($"UX Validation Tests: {passedUXTests}/{totalUXTests} passed");
            report.AppendLine($"UX Validations: {passedUXValidations}/{totalUXValidations} passed");
            report.AppendLine();

            // Quality Assessment
            report.AppendLine("🏆 QUALITY ASSESSMENT");
            report.AppendLine(new string('-', 40));
            
            var overallPassRate = (double)(passedIntegrationTests + passedUXTests) / (totalIntegrationTests + totalUXTests) * 100;
            var checkPassRate = (double)(passedIntegrationChecks + passedUXValidations) / (totalIntegrationChecks + totalUXValidations) * 100;

            report.AppendLine($"Overall Test Pass Rate: {overallPassRate:F1}%");
            report.AppendLine($"Individual Check Pass Rate: {checkPassRate:F1}%");
            
            string qualityGrade;
            if (overallPassRate >= 95 && checkPassRate >= 90)
                qualityGrade = "🥇 EXCELLENT (Production Ready)";
            else if (overallPassRate >= 85 && checkPassRate >= 80)
                qualityGrade = "🥈 GOOD (Minor Issues)";
            else if (overallPassRate >= 70 && checkPassRate >= 70)
                qualityGrade = "🥉 FAIR (Needs Improvement)";
            else
                qualityGrade = "❌ POOR (Major Issues)";

            report.AppendLine($"Quality Grade: {qualityGrade}");
            report.AppendLine();

            // Recommendations
            if (!suiteResult.OverallSuccess)
            {
                report.AppendLine("🔧 RECOMMENDATIONS");
                report.AppendLine(new string('-', 40));
                
                var failedIntegrationTests = suiteResult.IntegrationResults.Where(r => !r.Success).ToList();
                var failedUXTests = suiteResult.UXResults.Where(r => !r.Success).ToList();

                if (failedIntegrationTests.Any())
                {
                    report.AppendLine("Integration Issues:");
                    foreach (var test in failedIntegrationTests)
                    {
                        report.AppendLine($"  • Fix {test.TestName}: {test.ErrorMessage ?? "Check failed validations"}");
                    }
                }

                if (failedUXTests.Any())
                {
                    report.AppendLine("UX Issues:");
                    foreach (var test in failedUXTests)
                    {
                        report.AppendLine($"  • Improve {test.TestName}: {test.ErrorMessage ?? "Check failed validations"}");
                    }
                }
                report.AppendLine();
            }

            report.AppendLine("🎯 CONCLUSION");
            report.AppendLine(new string('-', 40));
            if (suiteResult.OverallSuccess)
            {
                report.AppendLine("🎉 All tests passed! The PC Optimizer Pro redesign is ready for deployment.");
                report.AppendLine("The application successfully delivers the premium, magical user experience");
                report.AppendLine("with clear value communication and professional-grade functionality.");
            }
            else
            {
                report.AppendLine("⚠️ Some tests failed. Please address the issues above before deployment.");
                report.AppendLine("Focus on the failed validations to ensure the premium user experience.");
            }

            return report.ToString();
        }

        /// <summary>
        /// Run a quick smoke test to verify basic functionality
        /// </summary>
        public async Task<bool> RunSmokeTestAsync()
        {
            try
            {
                _logger.Information("🔥 Running smoke test");

                // Test basic service availability
                var systemInfoTest = await _integrationTests.TestSystemInfoGathering();
                var smartAnalysisTest = await _integrationTests.TestSmartAnalysisEngine();

                var smokeTestPassed = systemInfoTest.Success && smartAnalysisTest.Success;

                _logger.Information("🔥 Smoke test {Result}", smokeTestPassed ? "PASSED" : "FAILED");
                return smokeTestPassed;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "🔥 Smoke test failed");
                return false;
            }
        }
    }

    public class TestSuiteResult
    {
        public bool OverallSuccess { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public List<TestResult> IntegrationResults { get; set; } = new();
        public List<UXTestResult> UXResults { get; set; } = new();
        public string DetailedReport { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }
}
