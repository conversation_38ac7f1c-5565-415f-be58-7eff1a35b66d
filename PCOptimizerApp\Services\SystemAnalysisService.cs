using System;
using System.IO;
using System.Linq;
using System.Management;
using System.Threading.Tasks;
using Microsoft.Win32;
using PCOptimizerApp.Models;
using Serilog;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service for analyzing system context to personalize AI explanations
    /// </summary>
    public class SystemAnalysisService : ISystemAnalysisService
    {
        private static readonly ILogger Logger = Log.ForContext<SystemAnalysisService>();
        
        public async Task<SystemAnalysisContext> AnalyzeSystemAsync()
        {
            try
            {
                Logger.Information("Starting system analysis for AI personalization");
                
                var context = new SystemAnalysisContext();
                
                // Analyze system hardware
                await AnalyzeHardwareAsync(context);
                
                // Analyze storage
                await AnalyzeStorageAsync(context);
                
                // Analyze temporary files
                await AnalyzeTemporaryFilesAsync(context);
                
                // Analyze startup programs
                await AnalyzeStartupProgramsAsync(context);
                
                // Estimate performance metrics
                EstimatePerformanceMetrics(context);
                
                Logger.Information("System analysis completed: {RAM}GB RAM, {CPU} cores, {Storage} storage", 
                    context.TotalRamGB, context.CpuCores, context.HasSsd ? "SSD" : "HDD");
                
                return context;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error during system analysis");
                return GetDefaultContext();
            }
        }

        private static async Task AnalyzeHardwareAsync(SystemAnalysisContext context)
        {
            await Task.Run(() =>
            {
                try
                {
                    // Get RAM information
                    using var searcher = new ManagementObjectSearcher("SELECT TotalVisibleMemorySize FROM Win32_OperatingSystem");
                    using var collection = searcher.Get();
                    foreach (ManagementObject obj in collection.Cast<ManagementObject>())
                    {
                        var totalMemoryKB = Convert.ToUInt64(obj["TotalVisibleMemorySize"]);
                        context.TotalRamGB = (int)(totalMemoryKB / 1024 / 1024);
                        break;
                    }

                    // Get CPU information
                    using var cpuSearcher = new ManagementObjectSearcher("SELECT NumberOfCores FROM Win32_Processor");
                    using var cpuCollection = cpuSearcher.Get();
                    foreach (ManagementObject obj in cpuCollection.Cast<ManagementObject>())
                    {
                        context.CpuCores = Convert.ToInt32(obj["NumberOfCores"]);
                        break;
                    }

                    // Get Windows version
                    context.WindowsVersion = Environment.OSVersion.Version.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Warning(ex, "Error analyzing hardware information");
                    context.TotalRamGB = 8; // Default values
                    context.CpuCores = 4;
                    context.WindowsVersion = "Windows 10/11";
                }
            });
        }

        private static async Task AnalyzeStorageAsync(SystemAnalysisContext context)
        {
            await Task.Run(() =>
            {
                try
                {
                    // Check if system drive is SSD
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
                    using var collection = searcher.Get();
                    foreach (ManagementObject obj in collection.Cast<ManagementObject>())
                    {
                        var mediaType = obj["MediaType"]?.ToString() ?? "";
                        if (mediaType.Contains("SSD") || mediaType.Contains("Solid State"))
                        {
                            context.HasSsd = true;
                            break;
                        }
                    }

                    // Get available storage space
                    var systemDrive = new DriveInfo(Environment.SystemDirectory[..1]);
                    if (systemDrive.IsReady)
                    {
                        context.AvailableStorageGB = systemDrive.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0);
                        context.UsedStoragePercent = (1.0 - (double)systemDrive.AvailableFreeSpace / systemDrive.TotalSize) * 100;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warning(ex, "Error analyzing storage information");
                    context.HasSsd = false; // Default to HDD
                    context.AvailableStorageGB = 100;
                    context.UsedStoragePercent = 50;
                }
            });
        }

        private static async Task AnalyzeTemporaryFilesAsync(SystemAnalysisContext context)
        {
            await Task.Run(() =>
            {
                try
                {
                    var tempPaths = new[]
                    {
                        Path.GetTempPath(),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Prefetch")
                    };

                    long totalSize = 0;
                    int totalFiles = 0;

                    foreach (var path in tempPaths.Where(Directory.Exists))
                    {
                        try
                        {
                            var files = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                            totalFiles += files.Length;
                            
                            foreach (var file in files)
                            {
                                try
                                {
                                    var fileInfo = new FileInfo(file);
                                    totalSize += fileInfo.Length;
                                }
                                catch
                                {
                                    // Skip files that can't be accessed
                                }
                            }
                        }
                        catch
                        {
                            // Skip directories that can't be accessed
                        }
                    }

                    context.TempFilesCount = totalFiles;
                    context.TempFilesSizeGB = totalSize / (1024.0 * 1024.0 * 1024.0);
                }
                catch (Exception ex)
                {
                    Logger.Warning(ex, "Error analyzing temporary files");
                    context.TempFilesCount = 500;
                    context.TempFilesSizeGB = 2.5;
                }
            });
        }

        private static async Task AnalyzeStartupProgramsAsync(SystemAnalysisContext context)
        {
            await Task.Run(() =>
            {
                try
                {
                    var startupLocations = new[]
                    {
                        @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                        @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run"
                    };

                    int startupCount = 0;

                    foreach (var location in startupLocations)
                    {
                        try
                        {
                            using var key = Registry.LocalMachine.OpenSubKey(location);
                            if (key != null)
                            {
                                startupCount += key.GetValueNames().Length;
                            }
                        }
                        catch
                        {
                            // Continue if registry access fails
                        }
                    }

                    // Also check current user startup
                    try
                    {
                        using var userKey = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run");
                        if (userKey != null)
                        {
                            startupCount += userKey.GetValueNames().Length;
                        }
                    }
                    catch
                    {
                        // Continue if registry access fails
                    }

                    context.StartupProgramsCount = startupCount;
                }
                catch (Exception ex)
                {
                    Logger.Warning(ex, "Error analyzing startup programs");
                    context.StartupProgramsCount = 12; // Default estimate
                }
            });
        }

        private static void EstimatePerformanceMetrics(SystemAnalysisContext context)
        {
            try
            {
                // Estimate current boot time based on startup programs and hardware
                var baseBootTime = context.HasSsd ? 20 : 40; // Base boot time
                var startupPenalty = Math.Max(0, context.StartupProgramsCount - 5) * 2; // 2 seconds per extra startup program
                var ramBonus = Math.Max(0, (context.TotalRamGB - 4) * -2); // Faster with more RAM
                
                context.CurrentBootTimeSeconds = baseBootTime + startupPenalty + ramBonus;
                
                // Estimate improvement after optimization
                var improvementFactor = Math.Min(0.6, context.StartupProgramsCount * 0.05); // Up to 60% improvement
                context.EstimatedBootTimeSeconds = (int)(context.CurrentBootTimeSeconds * (1 - improvementFactor));
                
                // Estimate general performance gain
                context.EstimatedPerformanceGain = (int)(improvementFactor * 100);
                if (context.EstimatedPerformanceGain < 5) context.EstimatedPerformanceGain = 5;
                if (context.EstimatedPerformanceGain > 25) context.EstimatedPerformanceGain = 25;
            }
            catch (Exception ex)
            {
                Logger.Warning(ex, "Error estimating performance metrics");
                context.CurrentBootTimeSeconds = 45;
                context.EstimatedBootTimeSeconds = 30;
                context.EstimatedPerformanceGain = 15;
            }
        }

        private static SystemAnalysisContext GetDefaultContext()
        {
            return new SystemAnalysisContext
            {
                TotalRamGB = 8,
                CpuCores = 4,
                WindowsVersion = "Windows 10/11",
                HasSsd = false,
                TempFilesCount = 500,
                TempFilesSizeGB = 2.5,
                StartupProgramsCount = 12,
                CurrentBootTimeSeconds = 45,
                EstimatedBootTimeSeconds = 30,
                EstimatedPerformanceGain = 15,
                AvailableStorageGB = 100,
                UsedStoragePercent = 50
            };
        }
    }

    public interface ISystemAnalysisService
    {
        Task<SystemAnalysisContext> AnalyzeSystemAsync();
    }
}
