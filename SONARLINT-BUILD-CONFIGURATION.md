# SonarLint Configuration for Build-Time Analysis Only

This configuration disables real-time SonarLint analysis to improve VS Code performance while ensuring code quality rules are enforced during build time.

## Current Configuration Applied:

### 1. **Disabled Real-Time Analysis**
```json
"sonarlint.connectedMode.automatic": false
"sonarlint.disableTelemetry": true
"sonarlint.trace.server": "off"
"sonarlint.output.showVerboseLogs": false
```

### 2. **Performance Optimizations**
```json
"files.watcherExclude": {
  "**/.git/objects/**": true,
  "**/node_modules/*/**": true,
  "**/bin/**": true,
  "**/obj/**": true,
  "**/.vs/**": true
}
```

### 3. **Focus on New Code Only**
```json
"sonarlint.focusOnNewCode": true
```

## To Enable Build-Time Analysis:

### Method 1: Using .NET Build Events (Recommended)

Add this to your `.csproj` file:

```xml
<PropertyGroup>
  <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  <WarningsAsErrors />
  <WarningsNotAsErrors />
  <RunAnalyzersDuringBuild>true</RunAnalyzersDuringBuild>
  <RunAnalyzersDuringLiveAnalysis>false</RunAnalyzersDuringLiveAnalysis>
</PropertyGroup>

<ItemGroup>
  <PackageReference Include="SonarAnalyzer.CSharp" Version="9.16.0.82469">
    <PrivateAssets>all</PrivateAssets>
    <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
  </PackageReference>
</ItemGroup>
```

### Method 2: Using EditorConfig

Create a `.editorconfig` file in your project root:

```ini
root = true

[*.cs]
# SonarLint rules during build
dotnet_analyzer_diagnostic.category-sonarway.severity = warning
dotnet_analyzer_diagnostic.category-codequality.severity = warning
dotnet_analyzer_diagnostic.category-maintainability.severity = suggestion
dotnet_analyzer_diagnostic.category-reliability.severity = warning
dotnet_analyzer_diagnostic.category-security.severity = error

# Specific SonarLint rules
dotnet_diagnostic.S1186.severity = warning  # Empty methods should be completed
dotnet_diagnostic.S1118.severity = warning  # Utility classes should not have public constructors
dotnet_diagnostic.S1134.severity = suggestion  # Track uses of "FIXME" tags
dotnet_diagnostic.S125.severity = warning   # Sections of code should not be commented out
```

### Method 3: Using MSBuild Targets

Add this to your project file or a `Directory.Build.props`:

```xml
<Target Name="SonarLintBuildAnalysis" BeforeTargets="Build">
  <Message Text="Running SonarLint analysis during build..." Importance="high" />
</Target>

<PropertyGroup>
  <EnableNETAnalyzers>true</EnableNETAnalyzers>
  <AnalysisLevel>latest</AnalysisLevel>
  <AnalysisMode>AllEnabledByDefault</AnalysisMode>
</PropertyGroup>
```

## Build Commands for Analysis:

### 1. **Standard Build with Analysis**
```powershell
dotnet build --verbosity normal
```

### 2. **Detailed Analysis Build**
```powershell
dotnet build --verbosity detailed --no-restore
```

### 3. **Clean Build with Full Analysis**
```powershell
dotnet clean
dotnet build --verbosity normal --configuration Release
```

## SonarLint Rules Configuration:

### Key Rules for .NET Projects:

```json
"sonarlint.rules": {
  "csharpsquid:S1186": { "level": "warn" },    // Empty methods
  "csharpsquid:S1118": { "level": "warn" },    // Utility classes
  "csharpsquid:S1134": { "level": "info" },    // FIXME tags
  "csharpsquid:S125": { "level": "warn" },     // Commented code
  "csharpsquid:S1481": { "level": "warn" },    // Unused variables
  "csharpsquid:S1172": { "level": "warn" },    // Unused parameters
  "csharpsquid:S1144": { "level": "warn" },    // Unused methods
  "csharpsquid:S101": { "level": "warn" },     // Class naming
  "csharpsquid:S1104": { "level": "error" },   // Public fields
  "csharpsquid:S3267": { "level": "warn" }     // Loops should be simplified
}
```

## Performance Benefits:

With this configuration, you'll experience:

1. **Faster Typing**: No real-time analysis delays
2. **Faster File Opening**: No analysis on file load
3. **Faster IntelliSense**: No interference from SonarLint
4. **Better Responsiveness**: Overall improved VS Code performance

## Quality Assurance:

Code quality is maintained through:

1. **Build-Time Warnings**: All SonarLint rules enforced during build
2. **CI/CD Integration**: Analysis in build pipelines
3. **Pre-Commit Hooks**: Optional git hooks for analysis
4. **Manual Analysis**: On-demand analysis when needed

## Manual Analysis (When Needed):

If you need to run SonarLint analysis manually:

### Command Palette (Ctrl+Shift+P):
- "SonarLint: Analyze all open files"
- "SonarLint: Clear SonarLint issues"
- "SonarLint: Show SonarLint output"

### VS Code Tasks:
Create a `.vscode/tasks.json` for manual analysis:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "SonarLint Analysis",
      "type": "shell",
      "command": "dotnet",
      "args": ["build", "--verbosity", "normal"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$msCompile"]
    }
  ]
}
```

## Integration with Your PC Optimizer Project:

For your PCOptimizerApp project, the build-time analysis will catch:

1. **Code Complexity**: Methods that are too complex
2. **Code Duplication**: Duplicate code blocks
3. **Security Issues**: Potential security vulnerabilities
4. **Performance Issues**: Code that could be optimized
5. **Maintainability**: Code that's hard to maintain

This approach gives you the best of both worlds: excellent VS Code performance during development and comprehensive code quality analysis during builds.
