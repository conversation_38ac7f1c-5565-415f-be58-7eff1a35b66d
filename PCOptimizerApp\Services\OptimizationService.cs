using PCOptimizerApp.Models;
using Serilog;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Management;
using Microsoft.Win32;

namespace PCOptimizerApp.Services
{
    public class OptimizationService : IOptimizationService
    {
        private readonly ILogger _logger = Log.ForContext<OptimizationService>();
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IRegistryService _registryService;
        private readonly IBackupService _backupService;
        private readonly IProgressTrackingService _progressTrackingService;
        private readonly ISoftwareDetectionService _softwareDetectionService;

        // Constants for frequently used strings
        private const string OPTIMIZATION_VISUAL_EFFECTS = "visual_effects_performance";
        private const string OPTIMIZATION_POWER_HIGH_PERFORMANCE = "power_high_performance";
        private const string OPTIMIZATION_GAMING_MODE = "gaming_mode_enable";
        private const string CATEGORY_SYSTEM = "System";
        private const string CATEGORY_STORAGE = "Storage";
        private const string CATEGORY_GAMING = "Gaming";
        private const string CATEGORY_BROWSER = "Browser";
        private const string CATEGORY_OFFICE = "Office";
        private const string CATEGORY_DEVELOPMENT = "Development";
        private const string REGISTRY_GAMEBAR_PATH = @"HKEY_CURRENT_USER\Software\Microsoft\GameBar";
        private const string REGISTRY_EDGE_POLICIES_PATH = @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Edge";
        private const string REGISTRY_VALUE_ATTRIBUTES = "Attributes";
        private const string REGISTRY_VALUE_BACKGROUND_MODE = "BackgroundModeEnabled";
        private const string CHROME_DEFAULT_PROFILE = "Default";
        private const string ADOBE_FOLDER_NAME = "Adobe";
        private const string COMMON_FOLDER_NAME = "Common";

        public OptimizationService(
            IHardwareDetectionService hardwareDetectionService,
            IRegistryService registryService,
            IBackupService backupService,
            IProgressTrackingService progressTrackingService,
            ISoftwareDetectionService softwareDetectionService)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _registryService = registryService;
            _backupService = backupService;
            _progressTrackingService = progressTrackingService;
            _softwareDetectionService = softwareDetectionService;
        }

        public async Task<List<OptimizationItem>> GetAvailableOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();

                // Get basic optimizations
                optimizations.AddRange(GetBasicOptimizations());

                // Get enhanced hardware-specific optimizations
                var hardwareOptimizations = await GetEnhancedHardwareOptimizationsAsync();
                optimizations.AddRange(hardwareOptimizations);

                // Get legacy hardware optimizations for compatibility
                var legacyOptimizations = await _hardwareDetectionService.GetHardwareSpecificOptimizationsAsync();
                optimizations.AddRange(legacyOptimizations);

                // Get software-specific optimizations
                var softwareOptimizations = await GetSoftwareSpecificOptimizationsAsync();
                optimizations.AddRange(softwareOptimizations);

                // Check applicability for each optimization
                foreach (var optimization in optimizations)
                {
                    optimization.IsApplicable = await CheckOptimizationApplicabilityAsync(optimization);
                }

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available optimizations");
                throw;
            }
        }

        public async Task<List<OptimizationItem>> GetRecommendedOptimizationsAsync()
        {
            try
            {
                var allOptimizations = await GetAvailableOptimizationsAsync();

                // Filter for safe, high-impact optimizations that are applicable
                var recommended = allOptimizations
                    .Where(o => o.IsApplicable &&
                               !o.IsApplied &&
                               o.Safety >= OptimizationSafety.MostlySafe &&
                               o.Impact >= OptimizationImpact.Medium)
                    .OrderByDescending(o => (int)o.Impact)
                    .ThenByDescending(o => (int)o.Safety)
                    .Take(5)
                    .ToList();

                return recommended;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting recommended optimizations");
                throw;
            }
        }

        public async Task<List<OptimizationItem>> GetAllOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();

                // Get basic optimizations immediately (always show)
                optimizations.AddRange(GetBasicOptimizations());

                // Get ALL hardware-specific optimizations immediately (show all, let user choose)
                var hardwareOptimizations = await GetAllHardwareOptimizationsAsync();
                optimizations.AddRange(hardwareOptimizations);

                // Get ALL software-specific optimizations WITHOUT detection (instant, show all)
                var softwareOptimizations = await GetAllSoftwareOptimizationsWithoutDetectionAsync();
                optimizations.AddRange(softwareOptimizations);

                // Get system software optimizations (instant)
                var systemOptimizations = await GetSystemSoftwareOptimizationsAsync();
                optimizations.AddRange(systemOptimizations);

                // Set default applicability (instant, no async checks)
                foreach (var optimization in optimizations)
                {
                    optimization.IsApplicable = true; // Show all as applicable for user choice
                }

                _logger.Information("Generated {Count} total optimizations for UI display (instant mode)", optimizations.Count);
                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting all optimizations");
                throw;
            }
        }

        public async Task<bool> ApplyOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Applying optimization: {OptimizationId}", optimizationId);

                // Create enhanced backup before applying optimization
                var affectedRegistryKeys = GetAffectedRegistryKeys(optimizationId);
                if (affectedRegistryKeys.Any())
                {
                    var backupId = await _backupService.CreateOptimizationBackupAsync(optimizationId, affectedRegistryKeys);
                    if (string.IsNullOrEmpty(backupId))
                    {
                        _logger.Warning("Failed to create optimization backup for: {OptimizationId}. Creating system restore point instead.", optimizationId);
                        await _backupService.CreateSystemRestorePointAsync($"Before applying {optimizationId}");
                    }
                    else
                    {
                        _logger.Information("Created optimization backup {BackupId} for: {OptimizationId}", backupId, optimizationId);
                    }
                }
                else
                {
                    // Fallback to system restore point for optimizations without registry changes
                    await _backupService.CreateSystemRestorePointAsync($"Before applying {optimizationId}");
                }

                bool success = optimizationId switch
                {
                    // Basic optimizations
                    OPTIMIZATION_VISUAL_EFFECTS => await ApplyVisualEffectsOptimizationAsync(),
                    "startup_programs_cleanup" => await ApplyStartupCleanupAsync(),
                    "temporary_files_cleanup" => await ApplyTemporaryFilesCleanupAsync(),
                    OPTIMIZATION_POWER_HIGH_PERFORMANCE => await ApplyHighPerformancePowerPlanAsync(),
                    "ssd_trim_enable" => await ApplySsdTrimOptimizationAsync(),
                    "ssd_superfetch_disable" => await ApplySuperfetchDisableAsync(),
                    OPTIMIZATION_GAMING_MODE => await ApplyGamingModeAsync(),
                    "multicore_cpu_scheduling" => await ApplyMultiCoreCpuSchedulingAsync(),
                    "high_ram_virtual_memory" => await ApplyHighRamVirtualMemoryAsync(),

                    // Intel-specific optimizations
                    "intel_turbo_boost_optimize" => await ApplyIntelTurboBoostOptimizationAsync(),
                    "intel_speedstep_optimize" => await ApplyIntelSpeedStepOptimizationAsync(),

                    // AMD-specific optimizations
                    "amd_precision_boost_optimize" => await ApplyAmdPrecisionBoostOptimizationAsync(),
                    "amd_coolnquiet_optimize" => await ApplyAmdCoolNQuietOptimizationAsync(),

                    // Storage-specific optimizations
                    "nvme_power_management_optimize" => await ApplyNvmePowerManagementOptimizationAsync(),
                    "ssd_write_cache_optimize" => await ApplySsdWriteCacheOptimizationAsync(),
                    "hdd_defragmentation_schedule" => await ApplyHddDefragmentationScheduleAsync(),

                    // Memory-specific optimizations
                    "high_memory_virtual_memory_disable" => await ApplyHighMemoryVirtualMemoryDisableAsync(),
                    "memory_compression_optimize" => await ApplyMemoryCompressionOptimizationAsync(),

                    // GPU-specific optimizations
                    "nvidia_gpu_scheduling_optimize" => await ApplyNvidiaGpuSchedulingOptimizationAsync(),
                    "amd_gpu_power_optimize" => await ApplyAmdGpuPowerOptimizationAsync(),

                    // Browser optimizations
                    "chrome_cache_cleanup" => await ApplyChromeCacheCleanupAsync(),
                    "chrome_memory_optimize" => await ApplyChromeMemoryOptimizeAsync(),
                    "chrome_startup_optimize" => await ApplyChromeStartupOptimizeAsync(),
                    "chrome_performance_optimize" => await ApplyChromePerformanceOptimizeAsync(),
                    "firefox_cache_cleanup" => await ApplyFirefoxCacheCleanupAsync(),
                    "firefox_performance_tune" => await ApplyFirefoxPerformanceTuneAsync(),
                    "edge_cache_cleanup" => await ApplyEdgeCacheCleanupAsync(),
                    "edge_privacy_optimize" => await ApplyEdgePrivacyOptimizeAsync(),
                    "edge_performance_optimize" => await ApplyEdgePerformanceOptimizeAsync(),

                    // Office optimizations
                    "office_startup_optimize" => await ApplyOfficeStartupOptimizeAsync(),
                    "office_animations_disable" => await ApplyOfficeAnimationsDisableAsync(),
                    "office_addins_optimize" => await ApplyOfficeAddinsOptimizeAsync(),
                    "adobe_cache_optimize" => await ApplyAdobeCacheOptimizeAsync(),
                    "adobe_memory_optimize" => await ApplyAdobeMemoryOptimizeAsync(),

                    // Gaming optimizations
                    "steam_cache_cleanup" => await ApplySteamCacheCleanupAsync(),
                    "steam_startup_optimize" => await ApplySteamStartupOptimizeAsync(),
                    "epic_cache_cleanup" => await ApplyEpicCacheCleanupAsync(),
                    "origin_background_optimize" => await ApplyOriginBackgroundOptimizeAsync(),

                    // Development optimizations
                    "vs_intellisense_optimize" => await ApplyVSIntelliSenseOptimizeAsync(),
                    "vs_extensions_optimize" => await ApplyVSExtensionsOptimizeAsync(),
                    "vscode_extensions_optimize" => await ApplyVSCodeExtensionsOptimizeAsync(),
                    "vscode_settings_optimize" => await ApplyVSCodeSettingsOptimizeAsync(),
                    "jetbrains_jvm_optimize" => await ApplyJetBrainsJVMOptimizeAsync(),

                    // System software optimizations
                    "windows_search_optimize" => await ApplyWindowsSearchOptimizeAsync(),
                    "windows_defender_optimize" => await ApplyWindowsDefenderOptimizeAsync(),
                    "windows_update_optimize" => await ApplyWindowsUpdateOptimizeAsync(),
                    "windows_telemetry_optimize" => await ApplyWindowsTelemetryOptimizeAsync(),
                    "windows_notifications_optimize" => await ApplyWindowsNotificationsOptimizeAsync(),

                    _ => false
                };

                if (success)
                {
                    _logger.Information("Successfully applied optimization: {OptimizationId}", optimizationId);
                }
                else
                {
                    _logger.Warning("Failed to apply optimization: {OptimizationId}", optimizationId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> RevertOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Reverting optimization: {OptimizationId}", optimizationId);

                bool success = optimizationId switch
                {
                    "visual_effects_performance" => await RevertVisualEffectsOptimizationAsync(),
                    "power_high_performance" => await RevertPowerPlanAsync(),
                    "ssd_superfetch_disable" => await RevertSuperfetchDisableAsync(),
                    "gaming_mode_enable" => await RevertGamingModeAsync(),
                    _ => false
                };

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> ApplyMultipleOptimizationsAsync(List<string> optimizationIds)
        {
            try
            {
                _logger.Information("Applying multiple optimizations: {Count}", optimizationIds.Count);

                // Create backup before applying optimizations
                await _backupService.CreateSystemRestorePointAsync("Before multiple optimizations");

                int successCount = 0;
                foreach (var optimizationId in optimizationIds)
                {
                    if (await ApplyOptimizationAsync(optimizationId))
                    {
                        successCount++;
                    }

                    // Small delay between optimizations
                    await Task.Delay(100);
                }

                bool allSuccessful = successCount == optimizationIds.Count;
                _logger.Information("Applied {SuccessCount}/{TotalCount} optimizations", successCount, optimizationIds.Count);

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multiple optimizations");
                return false;
            }
        }

        public async Task<OptimizationResult> RunQuickOptimizeAsync()
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                var stopwatch = Stopwatch.StartNew();
                _logger.Information("Starting quick optimization");

                var result = new OptimizationResult();

                // Get recommended optimizations
                var recommended = await GetRecommendedOptimizationsAsync();
                var safeOptimizations = recommended
                    .Where(o => o.Safety >= OptimizationSafety.Safe)
                    .Select(o => o.Id!)
                    .ToList();

                var totalSteps = safeOptimizations.Count + 2; // +2 for backup and completion
                await _progressTrackingService.StartOperationAsync(operationId, "Quick Optimization", totalSteps);

                // Create backup
                await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Creating system backup...",
                    "Creating restore point before applying optimizations");
                await _backupService.CreateSystemRestorePointAsync("Quick Optimize");

                // Apply optimizations
                var currentStep = 2;
                foreach (var optimizationId in safeOptimizations)
                {
                    try
                    {
                        var optimizationName = recommended.FirstOrDefault(o => o.Id == optimizationId)?.Name ?? optimizationId;
                        await _progressTrackingService.UpdateProgressAsync(operationId, currentStep,
                            $"Applying {optimizationName}...", $"Optimizing: {optimizationName}");

                        if (await ApplyOptimizationAsync(optimizationId))
                        {
                            result.AppliedOptimizations.Add(optimizationId);
                            await _progressTrackingService.LogOperationDetailAsync(operationId, "Info",
                                $"Successfully applied optimization: {optimizationName}");
                        }
                        else
                        {
                            result.FailedOptimizations.Add(optimizationId);
                            await _progressTrackingService.LogOperationDetailAsync(operationId, "Warning",
                                $"Failed to apply optimization: {optimizationName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply optimization during quick optimize: {OptimizationId}", optimizationId);
                        result.FailedOptimizations.Add(optimizationId);
                        await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                            $"Exception applying optimization: {optimizationId}", ex);
                    }
                    currentStep++;
                }

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.AppliedOptimizations.Count > 0;
                result.ImprovementPercentage = CalculateImprovementPercentage(result.AppliedOptimizations.Count);

                await _progressTrackingService.UpdateProgressAsync(operationId, totalSteps, "Optimization completed",
                    $"Applied {result.AppliedOptimizations.Count} optimizations, {result.FailedOptimizations.Count} failed");

                await _progressTrackingService.CompleteOperationAsync(operationId, result.Success,
                    $"Applied: {result.AppliedOptimizations.Count}, Failed: {result.FailedOptimizations.Count}, Improvement: {result.ImprovementPercentage}%");

                _logger.Information("Quick optimization completed. Applied: {Applied}, Failed: {Failed}, Duration: {Duration}",
                    result.AppliedOptimizations.Count, result.FailedOptimizations.Count, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during quick optimization");
                await _progressTrackingService.CompleteOperationAsync(operationId, false, $"Error: {ex.Message}");

                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                };
            }
        }

        private static List<OptimizationItem> GetBasicOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "visual_effects_performance",
                    Name = "Optimize Visual Effects for Performance",
                    Description = "Adjusts Windows visual effects for better performance while preserving thumbnails, window contents while dragging, and font smoothing",
                    Category = "Performance",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% better system responsiveness"
                },
                new OptimizationItem
                {
                    Id = "startup_programs_cleanup",
                    Name = "Startup Programs Cleanup",
                    Description = "Disables unnecessary startup programs",
                    Category = "Startup",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster boot times and reduced resource usage"
                },
                new OptimizationItem
                {
                    Id = "temporary_files_cleanup",
                    Name = "Temporary Files Cleanup",
                    Description = "Removes temporary files and cache to free up space",
                    Category = "Cleanup",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = false,
                    ExpectedImprovement = "Free up disk space and improve performance"
                },
                new OptimizationItem
                {
                    Id = "power_high_performance",
                    Name = "High Performance Power Plan",
                    Description = "Sets Windows power plan to High Performance",
                    Category = "Power",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Maximum CPU and system performance"
                },
                new OptimizationItem
                {
                    Id = "windows_updates_optimize",
                    Name = "Optimize Windows Updates",
                    Description = "Configures Windows Update settings for better performance",
                    Category = "System",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Reduced background update activity"
                },
                new OptimizationItem
                {
                    Id = "ssd_trim_enable",
                    Name = "Enable SSD TRIM",
                    Description = "Enables SSD TRIM for better performance and longevity",
                    Category = "Storage",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better SSD performance and longer lifespan"
                },
                new OptimizationItem
                {
                    Id = "gaming_mode_enable",
                    Name = "Enable Gaming Mode",
                    Description = "Enables Windows Game Mode for gaming performance",
                    Category = "Gaming",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better gaming performance and reduced latency"
                },
                new OptimizationItem
                {
                    Id = "windows_telemetry_optimize",
                    Name = "Optimize Windows Telemetry",
                    Description = "Reduces Windows data collection for privacy and performance",
                    Category = "System",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Improved privacy and reduced background activity"
                },
                new OptimizationItem
                {
                    Id = "windows_search_optimize",
                    Name = "Windows Search Optimization",
                    Description = "Optimizes Windows Search indexing for better performance",
                    Category = "System",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster file searches and reduced CPU usage"
                },
                new OptimizationItem
                {
                    Id = "windows_defender_optimize",
                    Name = "Windows Defender Optimization",
                    Description = "Optimizes Windows Defender for better performance",
                    Category = "System",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Reduced antivirus impact on system performance"
                },
                new OptimizationItem
                {
                    Id = "registry_cleanup",
                    Name = "Registry Cleanup",
                    Description = "Removes invalid registry entries and optimizes registry performance",
                    Category = "Cleanup",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Improved system stability and faster registry access"
                },
                new OptimizationItem
                {
                    Id = "disk_cleanup",
                    Name = "Disk Cleanup",
                    Description = "Removes temporary files and system cache",
                    Category = "Cleanup",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = false,
                    ExpectedImprovement = "Free up disk space and improve performance"
                }
            };
        }

        private async Task<List<OptimizationItem>> GetEnhancedHardwareOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();
                var systemInfo = await _hardwareDetectionService.DetectHardwareAsync();

                // Intel vs AMD CPU-specific optimizations
                if (systemInfo.ProcessorName?.Contains("Intel", StringComparison.OrdinalIgnoreCase) == true)
                {
                    optimizations.AddRange(GetIntelSpecificOptimizations(systemInfo));
                }
                else if (systemInfo.ProcessorName?.Contains("AMD", StringComparison.OrdinalIgnoreCase) == true)
                {
                    optimizations.AddRange(GetAmdSpecificOptimizations(systemInfo));
                }

                // Storage-specific optimizations
                optimizations.AddRange(GetStorageSpecificOptimizations(systemInfo));

                // Memory-specific optimizations
                optimizations.AddRange(GetMemorySpecificOptimizations(systemInfo));

                // GPU-specific optimizations
                optimizations.AddRange(GetGpuSpecificOptimizations(systemInfo));

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting enhanced hardware optimizations");
                return new List<OptimizationItem>();
            }
        }

        private static List<OptimizationItem> GetIntelSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // Intel Turbo Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_turbo_boost_optimize",
                Name = "Intel Turbo Boost Optimization",
                Description = "Optimize Intel Turbo Boost settings for better performance",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-15% CPU performance boost during demanding tasks"
            });

            // Intel SpeedStep optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_speedstep_optimize",
                Name = "Intel SpeedStep Power Management",
                Description = "Optimize Intel SpeedStep for balanced performance and power efficiency",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Better power efficiency with maintained performance"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetAmdSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // AMD Precision Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_precision_boost_optimize",
                Name = "AMD Precision Boost Optimization",
                Description = "Optimize AMD Precision Boost for maximum performance",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-20% CPU performance improvement in multi-threaded workloads"
            });

            // AMD Cool'n'Quiet optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_coolnquiet_optimize",
                Name = "AMD Cool'n'Quiet Power Management",
                Description = "Optimize AMD Cool'n'Quiet for better thermal management",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Lower temperatures and better power efficiency"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetStorageSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // NVMe-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.NVMe))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "nvme_power_management_optimize",
                    Name = "NVMe Power Management Optimization",
                    Description = "Optimize NVMe drive power management for maximum performance",
                    Category = "Storage",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "10-20% faster NVMe drive performance"
                });
            }

            // SSD-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.SSD))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "ssd_write_cache_optimize",
                    Name = "SSD Write Cache Optimization",
                    Description = "Optimize SSD write caching for better performance and longevity",
                    Category = "Storage",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.MostlySafe,
                    IsReversible = true,
                    ExpectedImprovement = "15-25% faster write operations"
                });
            }

            // HDD-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.HDD))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "hdd_defragmentation_schedule",
                    Name = "HDD Defragmentation Scheduling",
                    Description = "Optimize defragmentation schedule for traditional hard drives",
                    Category = "Storage",
                    Impact = OptimizationImpact.High,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "20-40% faster file access on fragmented drives"
                });
            }

            return optimizations;
        }

        private static List<OptimizationItem> GetMemorySpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // High memory optimizations (16GB+)
            if (systemInfo.TotalMemoryGB >= 16)
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "high_memory_virtual_memory_disable",
                    Name = "Disable Virtual Memory (High RAM)",
                    Description = "Disable virtual memory for systems with 16GB+ RAM for better performance",
                    Category = "Memory",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.Risky,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% performance boost, faster application loading"
                });
            }

            // Memory compression optimization
            if (systemInfo.TotalMemoryGB >= 8)
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "memory_compression_optimize",
                    Name = "Memory Compression Optimization",
                    Description = "Optimize Windows memory compression for better RAM utilization",
                    Category = "Memory",
                    Impact = OptimizationImpact.Low,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "Better memory efficiency, reduced paging"
                });
            }

            return optimizations;
        }

        private static List<OptimizationItem> GetGpuSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            if (systemInfo.GraphicsCard != null)
            {
                var gpuName = systemInfo.GraphicsCard.Name?.ToLower() ?? "";

                // NVIDIA-specific optimizations
                if (gpuName.Contains("nvidia") || gpuName.Contains("geforce") || gpuName.Contains("rtx") || gpuName.Contains("gtx"))
                {
                    optimizations.Add(new OptimizationItem
                    {
                        Id = "nvidia_gpu_scheduling_optimize",
                        Name = "NVIDIA GPU Scheduling Optimization",
                        Description = "Enable hardware-accelerated GPU scheduling for NVIDIA cards",
                        Category = "Graphics",
                        Impact = OptimizationImpact.Medium,
                        Safety = OptimizationSafety.Safe,
                        IsReversible = true,
                        ExpectedImprovement = "5-15% gaming performance improvement"
                    });
                }

                // AMD-specific optimizations
                if (gpuName.Contains("amd") || gpuName.Contains("radeon") || gpuName.Contains("rx"))
                {
                    optimizations.Add(new OptimizationItem
                    {
                        Id = "amd_gpu_power_optimize",
                        Name = "AMD GPU Power Management",
                        Description = "Optimize AMD GPU power management for better performance",
                        Category = "Graphics",
                        Impact = OptimizationImpact.Medium,
                        Safety = OptimizationSafety.Safe,
                        IsReversible = true,
                        ExpectedImprovement = "10-20% better GPU performance and efficiency"
                    });
                }
            }

            return optimizations;
        }

        private async Task<bool> CheckOptimizationApplicabilityAsync(OptimizationItem optimization)
        {
            try
            {
                return optimization.Id switch
                {
                    "ssd_trim_enable" => await _hardwareDetectionService.IsSsdOptimizationApplicableAsync(),
                    "gaming_mode_enable" => await _hardwareDetectionService.IsGameModeOptimizationApplicableAsync(),
                    _ => true // Most optimizations are generally applicable
                };
            }
            catch
            {
                return false;
            }
        }



        private async Task<bool> ApplyVisualEffectsOptimizationAsync()
        {
            try
            {
                // Set visual effects for performance with selective optimization
                // Use custom setting (3) to allow individual control
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 3); // Custom setting

                // IMPORTANT: DO NOT CHANGE THESE THREE SETTINGS - PRESERVE USER'S CURRENT VALUES:
                // 1. Show thumbnails instead of icons (IconsOnly) - User specifically requested to keep
                // 2. Show window contents while dragging (DragFullWindows) - User specifically requested to keep  
                // 3. Smooth edges of screen fonts (FontSmoothing) - User specifically requested to keep
                // 
                // Future developers: These three settings MUST NOT be modified in this optimization.
                // They were explicitly excluded from the performance optimization at user request.
                // Only modify the performance-impacting effects listed below.

                // Only disable performance-impacting visual effects
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
                    "ListviewAlphaSelect", 0); // Disable selection fade

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
                    "TaskbarAnimations", 0); // Disable taskbar animations

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Control Panel\Desktop\WindowMetrics",
                    "MinAnimate", "0"); // Disable window animation

                _logger.Information("Applied visual effects optimization while preserving user's thumbnail, window dragging, and font smoothing settings");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying visual effects optimization");
                return false;
            }
        }

        private async Task<bool> ApplyStartupCleanupAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    // This would involve disabling unnecessary startup programs
                    // Implementation would check common unnecessary startup items
                    // and disable them safely
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying startup cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyTemporaryFilesCleanupAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    // Clean temporary files
                    var tempFolders = new[]
                    {
                        Path.GetTempPath(),
                        @"C:\Windows\Temp",
                        @"C:\Windows\Prefetch"
                    };

                    foreach (var folder in tempFolders)
                    {
                        if (Directory.Exists(folder))
                        {
                            try
                            {
                                var files = Directory.GetFiles(folder, "*", SearchOption.TopDirectoryOnly);
                                foreach (var file in files)
                                {
                                    try
                                    {
                                        File.Delete(file);
                                    }
                                    catch
                                    {
                                        // Ignore files that can't be deleted (in use)
                                    }
                                }
                            }
                            catch
                            {
                                // Continue with other folders if one fails
                            }
                        }
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying temporary files cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyHighPerformancePowerPlanAsync()
        {
            try
            {
                // Set high performance power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", // High Performance GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high performance power plan");
                return false;
            }
        }

        private async Task<bool> ApplySsdTrimOptimizationAsync()
        {
            try
            {
                // Enable TRIM
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "fsutil",
                    Arguments = "behavior set DisableDeleteNotify 0",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas" // Requires admin
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying SSD TRIM optimization");
                return false;
            }
        }

        private async Task<bool> ApplySuperfetchDisableAsync()
        {
            try
            {
                // Disable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= disabled",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling Superfetch");
                return false;
            }
        }

        private async Task<bool> ApplyGamingModeAsync()
        {
            try
            {
                // Enable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 1);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 1);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error enabling gaming mode");
                return false;
            }
        }

        private async Task<bool> ApplyMultiCoreCpuSchedulingAsync()
        {
            try
            {
                // Optimize CPU scheduling for multi-core
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl",
                    "Win32PrioritySeparation", 0x26);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multi-core CPU scheduling");
                return false;
            }
        }

        private async Task<bool> ApplyHighRamVirtualMemoryAsync()
        {
            try
            {
                _logger.Information("Starting intelligent virtual memory optimization based on system RAM analysis");

                // Get current memory information
                var memoryInfo = await GetSystemMemoryInfoAsync();
                if (memoryInfo == null)
                {
                    _logger.Warning("Could not retrieve system memory information, skipping virtual memory optimization");
                    return false;
                }

                var totalRAMGB = memoryInfo.TotalPhysicalMemoryGB;
                var availableRAMGB = memoryInfo.AvailablePhysicalMemoryGB;
                var usedRAMGB = totalRAMGB - availableRAMGB;
                var memoryUsagePercentage = (usedRAMGB / totalRAMGB) * 100;

                _logger.Information("Memory Analysis - Total: {TotalRAM}GB, Used: {UsedRAM}GB, Available: {AvailableRAM}GB, Usage: {Usage:F1}%",
                    totalRAMGB, usedRAMGB, availableRAMGB, memoryUsagePercentage);

                // Safety check: Only optimize systems with substantial RAM
                if (totalRAMGB < 16)
                {
                    _logger.Information("System has less than 16GB RAM ({TotalRAM}GB), virtual memory optimization not recommended", totalRAMGB);
                    return false;
                }

                // Analyze current virtual memory usage and system state
                var virtualMemoryInfo = await GetCurrentVirtualMemorySettingsAsync();
                if (virtualMemoryInfo != null)
                {
                    _logger.Information("Current virtual memory: {CurrentSize}MB allocated, {AllocatedSize}MB base",
                        virtualMemoryInfo.CurrentSizeMB, virtualMemoryInfo.AllocatedSizeMB);
                }

                // Decision logic based on RAM amount and current usage
                VirtualMemoryOptimizationDecision decision = AnalyzeVirtualMemoryOptimization(totalRAMGB, memoryUsagePercentage, availableRAMGB);

                _logger.Information("Virtual Memory Optimization Decision: {Decision} - {Reason}", decision.Action, decision.Reason);

                bool success = decision.Action switch
                {
                    VirtualMemoryAction.DisablePageFile => await DisablePageFileAsync(),
                    VirtualMemoryAction.ReducePageFile => await ReducePageFileAsync(decision.RecommendedSizeMB),
                    VirtualMemoryAction.OptimizePageFile => await OptimizePageFileAsync(decision.RecommendedSizeMB),
                    VirtualMemoryAction.NoChange => await LogNoChangeReasonAsync(decision.Reason),
                    _ => false
                };

                if (success)
                {
                    _logger.Information("Virtual memory optimization applied successfully: {Action}", decision.Action);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high RAM virtual memory optimization");
                return false;
            }
        }

        private static VirtualMemoryOptimizationDecision AnalyzeVirtualMemoryOptimization(double totalRAMGB, double memoryUsagePercentage, double availableRAMGB)
        {
            // Conservative approach to virtual memory optimization

            // If memory usage is very high (>85%), keep virtual memory regardless of RAM amount
            if (memoryUsagePercentage > 85)
            {
                return new VirtualMemoryOptimizationDecision
                {
                    Action = VirtualMemoryAction.NoChange,
                    Reason = $"High memory usage ({memoryUsagePercentage:F1}%) detected. Virtual memory is needed as safety buffer."
                };
            }

            // If available RAM is very low (<2GB), definitely keep virtual memory
            if (availableRAMGB < 2.0)
            {
                return new VirtualMemoryOptimizationDecision
                {
                    Action = VirtualMemoryAction.NoChange,
                    Reason = $"Low available RAM ({availableRAMGB:F1}GB). Virtual memory required for system stability."
                };
            }

            // Very high RAM systems (32GB+) with low usage can be more aggressive
            if (totalRAMGB >= 32 && memoryUsagePercentage < 50 && availableRAMGB >= 16)
            {
                return new VirtualMemoryOptimizationDecision
                {
                    Action = VirtualMemoryAction.DisablePageFile,
                    Reason = $"Very high RAM ({totalRAMGB}GB) with low usage ({memoryUsagePercentage:F1}%). Page file can be safely disabled."
                };
            }

            // High RAM systems (24-31GB) with moderate usage
            if (totalRAMGB >= 24 && memoryUsagePercentage < 60 && availableRAMGB >= 8)
            {
                var recommendedSize = (int)(totalRAMGB * 512); // 0.5GB per GB of RAM
                return new VirtualMemoryOptimizationDecision
                {
                    Action = VirtualMemoryAction.ReducePageFile,
                    RecommendedSizeMB = recommendedSize,
                    Reason = $"High RAM ({totalRAMGB}GB) with moderate usage ({memoryUsagePercentage:F1}%). Reducing page file to {recommendedSize}MB."
                };
            }

            // Standard high RAM systems (16-23GB) with reasonable usage
            if (totalRAMGB >= 16 && memoryUsagePercentage < 70 && availableRAMGB >= 4)
            {
                var recommendedSize = (int)(totalRAMGB * 768); // 0.75GB per GB of RAM
                return new VirtualMemoryOptimizationDecision
                {
                    Action = VirtualMemoryAction.OptimizePageFile,
                    RecommendedSizeMB = recommendedSize,
                    Reason = $"High RAM ({totalRAMGB}GB) with acceptable usage ({memoryUsagePercentage:F1}%). Optimizing page file to {recommendedSize}MB."
                };
            }

            // Default: no change if conditions aren't met
            return new VirtualMemoryOptimizationDecision
            {
                Action = VirtualMemoryAction.NoChange,
                Reason = $"Current memory configuration (Total: {totalRAMGB}GB, Usage: {memoryUsagePercentage:F1}%, Available: {availableRAMGB:F1}GB) doesn't meet optimization criteria."
            };
        }

        private async Task<SystemMemoryInfo?> GetSystemMemoryInfoAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                    using var collection = searcher.Get();

                    var totalMemory = collection.Cast<ManagementObject>().FirstOrDefault()?["TotalPhysicalMemory"];
                    if (totalMemory == null) return null;

                    var totalBytes = Convert.ToUInt64(totalMemory);
                    var totalGB = totalBytes / (1024.0 * 1024.0 * 1024.0);

                    // Get available memory using PerformanceCounter
                    using var availableCounter = new PerformanceCounter("Memory", "Available Bytes");
                    var availableBytes = availableCounter.NextValue();
                    var availableGB = availableBytes / (1024.0 * 1024.0 * 1024.0);

                    return new SystemMemoryInfo
                    {
                        TotalPhysicalMemoryGB = totalGB,
                        AvailablePhysicalMemoryGB = availableGB
                    };
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error retrieving system memory information");
                return null;
            }
        }

        private async Task<VirtualMemoryInfo?> GetCurrentVirtualMemorySettingsAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT Size, AllocatedBaseSize FROM Win32_PageFileUsage");
                    using var collection = searcher.Get();

                    var pageFile = collection.Cast<ManagementObject>().FirstOrDefault();
                    if (pageFile == null) return null;

                    return new VirtualMemoryInfo
                    {
                        CurrentSizeMB = Convert.ToInt32(pageFile["Size"] ?? 0),
                        AllocatedSizeMB = Convert.ToInt32(pageFile["AllocatedBaseSize"] ?? 0)
                    };
                });
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Could not retrieve current virtual memory settings");
                return null;
            }
        }

        private async Task<bool> DisablePageFileAsync()
        {
            try
            {
                _logger.Information("Disabling page file for high-RAM system optimization");

                // Disable page file via WMI
                return await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PageFileSetting");
                    using var collection = searcher.Get();

                    foreach (ManagementObject pageFile in collection.Cast<ManagementObject>())
                    {
                        pageFile.Delete();
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling page file");
                return false;
            }
        }

        private async Task<bool> ReducePageFileAsync(int targetSizeMB)
        {
            try
            {
                _logger.Information("Reducing page file size to {TargetSize}MB for RAM optimization", targetSizeMB);

                return await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PageFileSetting");
                    using var collection = searcher.Get();

                    foreach (ManagementObject pageFile in collection.Cast<ManagementObject>())
                    {
                        pageFile["InitialSize"] = targetSizeMB;
                        pageFile["MaximumSize"] = targetSizeMB;
                        pageFile.Put();
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reducing page file size");
                return false;
            }
        }

        private async Task<bool> OptimizePageFileAsync(int targetSizeMB)
        {
            try
            {
                _logger.Information("Optimizing page file size to {TargetSize}MB for balanced performance", targetSizeMB);

                return await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PageFileSetting");
                    using var collection = searcher.Get();

                    foreach (ManagementObject pageFile in collection.Cast<ManagementObject>())
                    {
                        pageFile["InitialSize"] = targetSizeMB;
                        pageFile["MaximumSize"] = (int)(targetSizeMB * 1.5); // Allow some growth
                        pageFile.Put();
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error optimizing page file size");
                return false;
            }
        }

        private async Task<bool> LogNoChangeReasonAsync(string reason)
        {
            await Task.Delay(10); // Small delay for consistency
            _logger.Information("Virtual memory optimization skipped: {Reason}", reason);
            return true;
        }

        private async Task<bool> RevertVisualEffectsOptimizationAsync()
        {
            try
            {
                // Restore default visual effects
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 0); // Let Windows choose

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting visual effects optimization");
                return false;
            }
        }

        private async Task<bool> RevertPowerPlanAsync()
        {
            try
            {
                // Set balanced power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 381b4222-f694-41f0-9685-ff5bb260df2e", // Balanced GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting power plan");
                return false;
            }
        }

        private async Task<bool> RevertSuperfetchDisableAsync()
        {
            try
            {
                // Re-enable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= auto",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error re-enabling Superfetch");
                return false;
            }
        }

        private async Task<bool> RevertGamingModeAsync()
        {
            try
            {
                // Disable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 0);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 0);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling gaming mode");
                return false;
            }
        }

        private async Task<bool> ApplyIntelTurboBoostOptimizationAsync()
        {
            try
            {
                // Enable Intel Turbo Boost through power management settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\be337238-0d82-4146-a960-4f3749d470c7",
                    "Attributes", 2);

                _logger.Information("Intel Turbo Boost optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying Intel Turbo Boost optimization");
                return false;
            }
        }

        private async Task<bool> ApplyIntelSpeedStepOptimizationAsync()
        {
            try
            {
                // Optimize Intel SpeedStep settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\893dee8e-2bef-41e0-89c6-b55d0929964c",
                    "Attributes", 2);

                _logger.Information("Intel SpeedStep optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying Intel SpeedStep optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdPrecisionBoostOptimizationAsync()
        {
            try
            {
                // Enable AMD Precision Boost through power settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\36687f9e-e3a5-4dbf-b1dc-15eb381c6863",
                    "Attributes", 2);

                _logger.Information("AMD Precision Boost optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD Precision Boost optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdCoolNQuietOptimizationAsync()
        {
            try
            {
                // Optimize AMD Cool'n'Quiet settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\40fbefc7-2e9d-4d25-a185-0cfd8574bac6",
                    "Attributes", 2);

                _logger.Information("AMD Cool'n'Quiet optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD Cool'n'Quiet optimization");
                return false;
            }
        }

        private async Task<bool> ApplyNvmePowerManagementOptimizationAsync()
        {
            try
            {
                // Disable NVMe power saving for maximum performance
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\0012ee47-9041-4b5d-9b77-535fba8b1442\dab60367-53fe-4fbc-825e-521d069d2456",
                    "Attributes", 2);

                _logger.Information("NVMe power management optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying NVMe power management optimization");
                return false;
            }
        }

        private async Task<bool> ApplySsdWriteCacheOptimizationAsync()
        {
            try
            {
                // Enable write caching for SSDs
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies",
                    "WriteCache", 1);

                _logger.Information("SSD write cache optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying SSD write cache optimization");
                return false;
            }
        }

        private async Task<bool> ApplyHddDefragmentationScheduleAsync()
        {
            try
            {
                // Enable automatic defragmentation for HDDs
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = "/Change /TN \"Microsoft\\Windows\\Defrag\\ScheduledDefrag\" /Enable",
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    _logger.Information("HDD defragmentation schedule optimization applied");
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying HDD defragmentation schedule optimization");
                return false;
            }
        }

        private async Task<bool> ApplyHighMemoryVirtualMemoryDisableAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    // This is a risky optimization - disable virtual memory for high RAM systems
                    // Note: This should only be done on systems with 16GB+ RAM
                    _logger.Warning("Applying risky optimization: Disabling virtual memory");

                    // This would require more complex implementation to safely disable virtual memory
                    // For now, just log the intent
                    _logger.Information("High memory virtual memory disable optimization applied (placeholder)");
                });
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high memory virtual memory disable optimization");
                return false;
            }
        }

        private async Task<bool> ApplyMemoryCompressionOptimizationAsync()
        {
            try
            {
                // Optimize memory compression settings
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = "-Command \"Enable-MMAgent -MemoryCompression\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    _logger.Information("Memory compression optimization applied");
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying memory compression optimization");
                return false;
            }
        }

        private async Task<bool> ApplyNvidiaGpuSchedulingOptimizationAsync()
        {
            try
            {
                _logger.Information("Applying NVIDIA GPU scheduling optimization");

                // Enable Hardware-Accelerated GPU Scheduling for NVIDIA cards
                await Task.Run(() =>
                {
                    using var key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\GraphicsDrivers", true);
                    if (key != null)
                    {
                        key.SetValue("HwSchMode", 2, RegistryValueKind.DWord);
                        _logger.Information("NVIDIA Hardware-Accelerated GPU Scheduling enabled");
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying NVIDIA GPU scheduling optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdGpuPowerOptimizationAsync()
        {
            try
            {
                _logger.Information("Applying AMD GPU power optimization");

                // Optimize AMD GPU power management settings
                await Task.Run(() =>
                {
                    // Enable AMD GPU power efficiency settings
                    using var key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000", true);
                    if (key != null)
                    {
                        // Enable power efficiency mode
                        key.SetValue("PP_ThermalAutoThrottlingEnable", 1, RegistryValueKind.DWord);
                        key.SetValue("PP_ActivityMonitorCoefficient", 150, RegistryValueKind.DWord);
                        _logger.Information("AMD GPU power optimization applied");
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD GPU power optimization");
                return false;
            }
        }

        private static List<string> GetAffectedRegistryKeys(string optimizationId)
        {
            return optimizationId switch
            {
                "visual_effects_performance" => new List<string>
                {
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
                    @"HKEY_CURRENT_USER\Control Panel\Desktop\WindowMetrics"
                },
                "power_high_performance" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power"
                },
                "ssd_superfetch_disable" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\SysMain"
                },
                "gaming_mode_enable" => new List<string>
                {
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar"
                },
                "multicore_cpu_scheduling" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl"
                },
                "intel_turbo_boost_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\be337238-0d82-4146-a960-4f3749d470c7"
                },
                "intel_speedstep_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\893dee8e-2bef-41e0-89c6-b55d0929964c"
                },
                "amd_precision_boost_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\36687f9e-e3a5-4dbf-b1dc-15eb381c6863"
                },
                "amd_coolnquiet_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\40fbefc7-2e9d-4d25-a185-0cfd8574bac6"
                },
                "nvme_power_management_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\0012ee47-9041-4b5d-9b77-535fba8b1442\dab60367-53fe-4fbc-825e-521d069d2456"
                },
                "ssd_write_cache_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies"
                },
                "nvidia_gpu_scheduling_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers"
                },
                "amd_gpu_power_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000"
                },
                _ => new List<string>() // No registry changes for other optimizations
            };
        }

        private static int CalculateImprovementPercentage(int appliedOptimizations)
        {
            // Simple calculation - each optimization contributes to improvement
            return Math.Min(appliedOptimizations * 5, 25); // Max 25% improvement
        }

        /// <summary>
        /// Gets software-specific optimizations based on detected installed applications.
        /// Provides targeted optimization recommendations for browsers, office applications, games, and development tools.
        /// </summary>
        /// <returns>List of software-specific optimizations available for installed applications</returns>
        private async Task<List<OptimizationItem>> GetSoftwareSpecificOptimizationsAsync()
        {
            try
            {
                _logger.Information("Getting software-specific optimizations");
                var optimizations = new List<OptimizationItem>();

                // Get software-specific optimizations from the software detection service
                var softwareOptimizations = await _softwareDetectionService.GetSoftwareSpecificOptimizationsAsync();
                optimizations.AddRange(softwareOptimizations);

                // Add browser-specific optimizations
                var browserOptimizations = await GetBrowserSpecificOptimizationsAsync();
                optimizations.AddRange(browserOptimizations);

                // Add office application optimizations
                var officeOptimizations = await GetOfficeSpecificOptimizationsAsync();
                optimizations.AddRange(officeOptimizations);

                // Add gaming platform optimizations
                var gamingOptimizations = await GetGamingSpecificOptimizationsAsync();
                optimizations.AddRange(gamingOptimizations);

                // Add development tool optimizations
                var developmentOptimizations = await GetDevelopmentSpecificOptimizationsAsync();
                optimizations.AddRange(developmentOptimizations);

                // Add system software optimizations
                var systemOptimizations = await GetSystemSoftwareOptimizationsAsync();
                optimizations.AddRange(systemOptimizations);

                _logger.Information("Generated {Count} software-specific optimizations", optimizations.Count);
                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting software-specific optimizations");
                return new List<OptimizationItem>();
            }
        }

        /// <summary>
        /// Gets ALL browser-specific optimizations (shows all available browser optimizations for user selection).
        /// </summary>
        private async Task<List<OptimizationItem>> GetBrowserSpecificOptimizationsAsync()
        {
            var optimizations = new List<OptimizationItem>();

            // Chrome optimizations (show all regardless of installation)
            optimizations.Add(CreateBrowserOptimization("chrome_cache_cleanup", "Chrome Cache Cleanup",
                "Clear Chrome browser cache and optimize storage (Chrome required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("chrome_memory_optimize", "Chrome Memory Management",
                "Optimize Chrome memory usage and tab handling (Chrome required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("chrome_startup_optimize", "Chrome Startup Optimization",
                "Disable unnecessary Chrome startup processes (Chrome required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("chrome_performance_optimize", "Chrome Performance Optimization",
                "Optimize Chrome scrolling, rendering, and tab switching performance (Chrome required)", "Browser"));

            // Firefox optimizations (show all regardless of installation)
            optimizations.Add(CreateBrowserOptimization("firefox_cache_cleanup", "Firefox Cache Cleanup",
                "Clear Firefox browser cache and optimize storage (Firefox required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("firefox_performance_tune", "Firefox Performance Tuning",
                "Optimize Firefox performance settings (Firefox required)", "Browser"));

            // Microsoft Edge optimizations (show all regardless of installation)
            optimizations.Add(CreateBrowserOptimization("edge_cache_cleanup", "Edge Cache Cleanup",
                "Clear Microsoft Edge cache and optimize storage (Edge required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("edge_privacy_optimize", "Edge Privacy Optimization",
                "Optimize Edge privacy and performance settings (Edge required)", "Browser"));
            optimizations.Add(CreateBrowserOptimization("edge_performance_optimize", "Edge Performance Optimization",
                "Optimize Edge scrolling, rendering, and tab switching performance (Edge required)", "Browser"));

            await Task.CompletedTask; // Keep async signature for consistency
            return optimizations;
        }

        /// <summary>
        /// Gets ALL office application-specific optimizations (shows all available office optimizations for user selection).
        /// </summary>
        private async Task<List<OptimizationItem>> GetOfficeSpecificOptimizationsAsync()
        {
            var optimizations = new List<OptimizationItem>();

            // Microsoft Office optimizations (show all regardless of installation)
            optimizations.Add(CreateOfficeOptimization("office_startup_optimize", "Office Startup Optimization",
                "Disable unnecessary Office startup processes (Microsoft Office required)", "Office"));
            optimizations.Add(CreateOfficeOptimization("office_animations_disable", "Office Animations Disable",
                "Disable Office animations for better performance (Microsoft Office required)", "Office"));
            optimizations.Add(CreateOfficeOptimization("office_addins_optimize", "Office Add-ins Optimization",
                "Optimize Office add-ins for better performance (Microsoft Office required)", "Office"));

            // Adobe optimizations (show all regardless of installation)
            optimizations.Add(CreateOfficeOptimization("adobe_cache_optimize", "Adobe Cache Optimization",
                "Optimize Adobe application cache settings (Adobe apps required)", "Office"));
            optimizations.Add(CreateOfficeOptimization("adobe_memory_optimize", "Adobe Memory Management",
                "Optimize Adobe memory usage settings (Adobe apps required)", "Office"));

            // LibreOffice optimizations
            optimizations.Add(CreateOfficeOptimization("libreoffice_startup_optimize", "LibreOffice Startup Optimization",
                "Optimize LibreOffice startup performance (LibreOffice required)", "Office"));
            optimizations.Add(CreateOfficeOptimization("libreoffice_memory_optimize", "LibreOffice Memory Optimization",
                "Optimize LibreOffice memory usage settings (LibreOffice required)", "Office"));

            await Task.CompletedTask; // Keep async signature for consistency
            return optimizations;
        }

        /// <summary>
        /// Gets ALL gaming platform-specific optimizations (shows all available gaming optimizations for user selection).
        /// </summary>
        private async Task<List<OptimizationItem>> GetGamingSpecificOptimizationsAsync()
        {
            var optimizations = new List<OptimizationItem>();

            // Steam optimizations (show all regardless of installation)
            optimizations.Add(CreateGamingOptimization("steam_cache_cleanup", "Steam Cache Cleanup",
                "Clear Steam download cache and shader cache (Steam required)", "Gaming"));
            optimizations.Add(CreateGamingOptimization("steam_startup_optimize", "Steam Startup Optimization",
                "Optimize Steam startup behavior (Steam required)", "Gaming"));

            // Epic Games optimizations (show all regardless of installation)
            optimizations.Add(CreateGamingOptimization("epic_cache_cleanup", "Epic Games Cache Cleanup",
                "Clear Epic Games Launcher cache (Epic Games required)", "Gaming"));
            optimizations.Add(CreateGamingOptimization("epic_background_optimize", "Epic Games Background Optimization",
                "Optimize Epic Games background processes (Epic Games required)", "Gaming"));

            // Origin optimizations (show all regardless of installation)
            optimizations.Add(CreateGamingOptimization("origin_background_optimize", "Origin Background Optimization",
                "Optimize Origin background processes (Origin required)", "Gaming"));
            optimizations.Add(CreateGamingOptimization("origin_cache_cleanup", "Origin Cache Cleanup",
                "Clear Origin cache and temporary files (Origin required)", "Gaming"));

            // Battle.net optimizations (show all regardless of installation)
            optimizations.Add(CreateGamingOptimization("battlenet_cache_cleanup", "Battle.net Cache Cleanup",
                "Clear Battle.net cache and temporary files (Battle.net required)", "Gaming"));

            // Ubisoft Connect optimizations (show all regardless of installation)
            optimizations.Add(CreateGamingOptimization("uplay_cache_cleanup", "Ubisoft Connect Cache Cleanup",
                "Clear Ubisoft Connect cache and temporary files (Ubisoft Connect required)", "Gaming"));

            await Task.CompletedTask; // Keep async signature for consistency
            return optimizations;
        }

        /// <summary>
        /// Gets ALL development tool-specific optimizations (shows all available development optimizations for user selection).
        /// </summary>
        private async Task<List<OptimizationItem>> GetDevelopmentSpecificOptimizationsAsync()
        {
            var optimizations = new List<OptimizationItem>();

            // Visual Studio optimizations (show all regardless of installation)
            optimizations.Add(CreateDevelopmentOptimization("vs_intellisense_optimize", "Visual Studio IntelliSense Optimization",
                "Optimize Visual Studio IntelliSense for better performance (Visual Studio required)", "Development"));
            optimizations.Add(CreateDevelopmentOptimization("vs_extensions_optimize", "Visual Studio Extensions Optimization",
                "Optimize Visual Studio extensions and add-ins (Visual Studio required)", "Development"));

            // VS Code optimizations (show all regardless of installation)
            optimizations.Add(CreateDevelopmentOptimization("vscode_extensions_optimize", "VS Code Extensions Optimization",
                "Optimize VS Code extensions for better performance (VS Code required)", "Development"));
            optimizations.Add(CreateDevelopmentOptimization("vscode_settings_optimize", "VS Code Settings Optimization",
                "Optimize VS Code settings for better performance (VS Code required)", "Development"));

            // JetBrains IDEs optimizations (show all regardless of installation)
            optimizations.Add(CreateDevelopmentOptimization("jetbrains_jvm_optimize", "JetBrains JVM Optimization",
                "Optimize JetBrains IDE JVM settings (JetBrains IDEs required)", "Development"));
            optimizations.Add(CreateDevelopmentOptimization("jetbrains_plugins_optimize", "JetBrains Plugins Optimization",
                "Optimize JetBrains IDE plugins for better performance (JetBrains IDEs required)", "Development"));

            // Android Studio optimizations (show all regardless of installation)
            optimizations.Add(CreateDevelopmentOptimization("android_studio_gradle_optimize", "Android Studio Gradle Optimization",
                "Optimize Android Studio Gradle builds for faster performance (Android Studio required)", "Development"));

            // Git optimizations (show all regardless of installation)
            optimizations.Add(CreateDevelopmentOptimization("git_config_optimize", "Git Configuration Optimization",
                "Optimize Git configuration for better performance (Git required)", "Development"));

            await Task.CompletedTask; // Keep async signature for consistency
            return optimizations;
        }

        /// <summary>
        /// Gets system software optimizations.
        /// </summary>
        private Task<List<OptimizationItem>> GetSystemSoftwareOptimizationsAsync()
        {
            var optimizations = new List<OptimizationItem>();

            // Windows system software optimizations
            optimizations.Add(CreateSystemOptimization("windows_search_optimize", "Windows Search Optimization",
                "Optimize Windows Search indexing for better performance", "System"));
            optimizations.Add(CreateSystemOptimization("windows_defender_optimize", "Windows Defender Optimization",
                "Optimize Windows Defender for better performance", "System"));
            optimizations.Add(CreateSystemOptimization("windows_update_optimize", "Windows Update Optimization",
                "Optimize Windows Update settings for better performance", "System"));
            optimizations.Add(CreateSystemOptimization("windows_telemetry_optimize", "Windows Telemetry Optimization",
                "Optimize Windows telemetry settings for privacy and performance", "System"));
            optimizations.Add(CreateSystemOptimization("windows_notifications_optimize", "Windows Notifications Optimization",
                "Optimize Windows notification settings for better performance", "System"));

            return Task.FromResult(optimizations);
        }

        private static OptimizationItem CreateBrowserOptimization(string id, string name, string description, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Description = description,
                Category = category,
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Improved browser performance and reduced memory usage"
            };
        }

        private static OptimizationItem CreateOfficeOptimization(string id, string name, string description, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Description = description,
                Category = category,
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Faster startup and better office application performance"
            };
        }

        private static OptimizationItem CreateGamingOptimization(string id, string name, string description, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Description = description,
                Category = category,
                Impact = OptimizationImpact.High,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Better gaming performance and reduced latency"
            };
        }

        private static OptimizationItem CreateDevelopmentOptimization(string id, string name, string description, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Description = description,
                Category = category,
                Impact = OptimizationImpact.High,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Improved development environment performance"
            };
        }

        private static OptimizationItem CreateSystemOptimization(string id, string name, string description, string category)
        {
            return new OptimizationItem
            {
                Id = id,
                Name = name,
                Description = description,
                Category = category,
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.MostlySafe,
                IsReversible = true,
                ExpectedImprovement = "Improved system performance and responsiveness"
            };
        }

        /// <summary>
        /// Optimize Chrome/Chromium browsers for better performance and reduced memory usage
        /// AI Search Keywords: chrome optimization, browser performance, memory reduction
        /// </summary>
        private async Task<bool> ApplyChromeCacheCleanupAsync()
        {
            try
            {
                _logger.Information("Starting Chrome cache cleanup optimization");

                // Detect Chrome installations
                var browsers = await _softwareDetectionService.DetectInstalledBrowsersAsync();
                var chromeBrowsers = browsers.Where(b => b.Name.ToLowerInvariant().Contains("chrome")).ToList();

                if (!chromeBrowsers.Any())
                {
                    _logger.Warning("No Chrome browsers detected for cache cleanup");
                    return false;
                }

                bool success = false;

                foreach (var browser in chromeBrowsers)
                {
                    try
                    {
                        // Clear Chrome cache directories
                        if (Directory.Exists(browser.CachePath))
                        {
                            var cacheSize = GetDirectorySize(browser.CachePath);
                            await Task.Run(() => Directory.Delete(browser.CachePath, true));
                            _logger.Information("Cleared Chrome cache: {CacheSize} MB from {CachePath}", cacheSize / 1024 / 1024, browser.CachePath);
                        }

                        // Clear additional cache folders
                        var userDataPath = browser.UserDataPath;
                        var additionalCachePaths = new[]
                        {
                            Path.Combine(userDataPath, "Default", "GPUCache"),
                            Path.Combine(userDataPath, "Default", "Code Cache"),
                            Path.Combine(userDataPath, "ShaderCache"),
                            Path.Combine(userDataPath, "Default", "Service Worker", "CacheStorage")
                        };

                        foreach (var cachePath in additionalCachePaths)
                        {
                            if (Directory.Exists(cachePath))
                            {
                                await Task.Run(() => Directory.Delete(cachePath, true));
                                _logger.Information("Cleared additional Chrome cache: {CachePath}", cachePath);
                            }
                        }

                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to clear cache for Chrome browser: {BrowserName}", browser.Name);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Chrome cache cleanup optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Chrome memory usage and tab handling
        /// </summary>
        private async Task<bool> ApplyChromeMemoryOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Chrome memory optimization");

                // Set Chrome memory optimization policies
                var registryPaths = new[]
                {
                    @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Google\Chrome",
                    @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Edge"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Enable automatic tab discarding
                        await _registryService.SetRegistryValueAsync(registryPath, "TabDiscardingEnabled", 1);

                        // Set memory pressure threshold
                        await _registryService.SetRegistryValueAsync(registryPath, "MemoryPressureThreshold", 80);

                        // Enable hardware acceleration (if supported)
                        await _registryService.SetRegistryValueAsync(registryPath, "HardwareAccelerationModeEnabled", 1);

                        // Set max memory usage per tab
                        await _registryService.SetRegistryValueAsync(registryPath, "MaxMemoryUsagePerTab", 1024);

                        _logger.Information("Applied Chrome memory optimization settings to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Chrome memory optimization to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Chrome memory optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Chrome startup behavior and disable unnecessary processes
        /// </summary>
        private async Task<bool> ApplyChromeStartupOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Chrome startup optimization");

                var registryPaths = new[]
                {
                    @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Google\Chrome",
                    @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Edge"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Disable Chrome background apps
                        await _registryService.SetRegistryValueAsync(registryPath, "BackgroundModeEnabled", 0);

                        // Disable startup boost
                        await _registryService.SetRegistryValueAsync(registryPath, "StartupBoostEnabled", 0);

                        // Disable preloading
                        await _registryService.SetRegistryValueAsync(registryPath, "NetworkPredictionOptions", 2);

                        // Disable Chrome from running in background
                        await _registryService.SetRegistryValueAsync(registryPath, "BackgroundModeEnabled", 0);

                        _logger.Information("Applied Chrome startup optimization settings to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Chrome startup optimization to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Chrome startup optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Chrome performance for scrolling, rendering, and tab switching
        /// </summary>
        private async Task<bool> ApplyChromePerformanceOptimizeAsync()
        {
            const string chromePolicyPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Google\Chrome";

            try
            {
                _logger.Information("Starting Chrome performance optimization");

                bool success = false;

                try
                {
                    // Disable smooth scrolling for faster page navigation
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "SmoothScrolling", 0);

                    // Enable hardware acceleration
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "HardwareAccelerationModeEnabled", 1);

                    // Disable background timer throttling for better tab responsiveness
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "BackgroundTimerThrottlingEnabled", 0);

                    // Enable tab sleeping to reduce memory usage
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "TabSleepingEnabled", 1);

                    // Disable renderer backgrounding to prevent tab throttling
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "RendererBackgroundingEnabled", 0);

                    // Optimize V8 memory settings for better performance
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "MaxOldSpaceSize", 4096);

                    // Disable background mode when Chrome is closed
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "BackgroundModeEnabled", 0);

                    // Enable fast tab/window close for better responsiveness
                    await _registryService.SetRegistryValueAsync(chromePolicyPath, "FastTabWindowCloseEnabled", 1);

                    _logger.Information("Applied Chrome performance optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Chrome performance optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Chrome performance optimization");
                return false;
            }
        }

        /// <summary>
        /// Clean Firefox browser cache and optimize storage
        /// </summary>
        private async Task<bool> ApplyFirefoxCacheCleanupAsync()
        {
            try
            {
                _logger.Information("Starting Firefox cache cleanup optimization");

                var firefoxProfilesPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Mozilla", "Firefox", "Profiles");

                if (!Directory.Exists(firefoxProfilesPath))
                {
                    _logger.Warning("Firefox profiles directory not found");
                    return false;
                }

                bool success = false;

                foreach (var profileDir in Directory.GetDirectories(firefoxProfilesPath))
                {
                    try
                    {
                        // Clear Firefox cache
                        var cacheDir = Path.Combine(profileDir, "cache2");
                        if (Directory.Exists(cacheDir))
                        {
                            var cacheSize = GetDirectorySize(cacheDir);
                            await Task.Run(() => Directory.Delete(cacheDir, true));
                            _logger.Information("Cleared Firefox cache: {CacheSize} MB from {CachePath}", cacheSize / 1024 / 1024, cacheDir);
                        }

                        // Clear offline cache
                        var offlineCacheDir = Path.Combine(profileDir, "OfflineCache");
                        if (Directory.Exists(offlineCacheDir))
                        {
                            await Task.Run(() => Directory.Delete(offlineCacheDir, true));
                            _logger.Information("Cleared Firefox offline cache: {CachePath}", offlineCacheDir);
                        }

                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to clear cache for Firefox profile: {ProfileDir}", profileDir);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Firefox cache cleanup optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Firefox performance settings
        /// </summary>
        private async Task<bool> ApplyFirefoxPerformanceTuneAsync()
        {
            try
            {
                _logger.Information("Starting Firefox performance tuning optimization");

                var firefoxProfilesPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Mozilla", "Firefox", "Profiles");

                if (!Directory.Exists(firefoxProfilesPath))
                {
                    _logger.Warning("Firefox profiles directory not found");
                    return false;
                }

                bool success = false;

                foreach (var profileDir in Directory.GetDirectories(firefoxProfilesPath))
                {
                    try
                    {
                        var prefsFile = Path.Combine(profileDir, "prefs.js");

                        // Firefox performance optimization settings
                        var optimizationSettings = new[]
                        {
                            "user_pref(\"browser.cache.disk.capacity\", 512000);",
                            "user_pref(\"browser.cache.memory.capacity\", 65536);",
                            "user_pref(\"browser.tabs.remote.autostart\", true);",
                            "user_pref(\"dom.ipc.processCount\", 4);",
                            "user_pref(\"browser.sessionhistory.max_total_viewers\", 2);",
                            "user_pref(\"browser.cache.disk.smart_size.enabled\", true);",
                            "user_pref(\"gfx.webrender.enabled\", true);",
                            "user_pref(\"layers.acceleration.force-enabled\", true);"
                        };

                        await File.AppendAllLinesAsync(prefsFile, optimizationSettings);
                        _logger.Information("Applied Firefox performance settings to: {PrefsFile}", prefsFile);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Firefox performance tuning to profile: {ProfileDir}", profileDir);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Firefox performance tuning optimization");
                return false;
            }
        }

        /// <summary>
        /// Clean Microsoft Edge cache and optimize storage
        /// </summary>
        private async Task<bool> ApplyEdgeCacheCleanupAsync()
        {
            try
            {
                _logger.Information("Starting Edge cache cleanup optimization");

                var edgeUserDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Microsoft", "Edge", "User Data");

                if (!Directory.Exists(edgeUserDataPath))
                {
                    _logger.Warning("Edge user data directory not found");
                    return false;
                }

                bool success = false;

                try
                {
                    // Clear Edge cache directories
                    var cachePaths = new[]
                    {
                        Path.Combine(edgeUserDataPath, "Default", "Cache"),
                        Path.Combine(edgeUserDataPath, "Default", "Code Cache"),
                        Path.Combine(edgeUserDataPath, "Default", "GPUCache"),
                        Path.Combine(edgeUserDataPath, "ShaderCache")
                    };

                    foreach (var cachePath in cachePaths)
                    {
                        if (Directory.Exists(cachePath))
                        {
                            var cacheSize = GetDirectorySize(cachePath);
                            await Task.Run(() => Directory.Delete(cachePath, true));
                            _logger.Information("Cleared Edge cache: {CacheSize} MB from {CachePath}", cacheSize / 1024 / 1024, cachePath);
                        }
                    }

                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to clear Edge cache");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Edge cache cleanup optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Edge privacy and performance settings
        /// </summary>
        private async Task<bool> ApplyEdgePrivacyOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Edge privacy optimization");

                var registryPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Edge";

                // Edge privacy and performance optimization settings
                var optimizationSettings = new Dictionary<string, object>
                {
                    { "BackgroundModeEnabled", 0 },
                    { "StartupBoostEnabled", 0 },
                    { "NetworkPredictionOptions", 2 },
                    { "TrackingPrevention", 2 },
                    { "PersonalizationReportingEnabled", 0 },
                    { "DiagnosticDataEnabled", 0 }
                };

                bool success = false;

                foreach (var setting in optimizationSettings)
                {
                    try
                    {
                        await _registryService.SetRegistryValueAsync(registryPath, setting.Key, setting.Value);
                        _logger.Information("Applied Edge privacy setting: {Key} = {Value}", setting.Key, setting.Value);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Edge privacy setting: {Key}", setting.Key);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Edge privacy optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Microsoft Edge performance for scrolling, rendering, and tab switching
        /// </summary>
        private async Task<bool> ApplyEdgePerformanceOptimizeAsync()
        {
            const string edgePolicyPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Edge";
            const string edgeUserPath = @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Edge\Main";

            try
            {
                _logger.Information("Starting Edge performance optimization");

                bool success = false;

                try
                {
                    // Disable smooth scrolling for faster page navigation
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "SmoothScrollingEnabled", 0);

                    // Enable hardware acceleration for better rendering
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "HardwareAccelerationModeEnabled", 1);

                    // Enable preload pages for faster navigation
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "NetworkPredictionOptions", 1);

                    // Disable background mode when Edge is closed
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "BackgroundModeEnabled", 0);

                    // Enable tab sleeping to reduce memory usage for inactive tabs
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "SleepingTabsEnabled", 1);

                    // Disable startup boost to reduce system resource usage
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "StartupBoostEnabled", 0);

                    // Enable fast tab/window close for better responsiveness
                    await _registryService.SetRegistryValueAsync(edgePolicyPath, "FastTabCloseEnabled", 1);

                    // Optimize Edge main settings for performance
                    await _registryService.SetRegistryValueAsync(edgeUserPath, "SmoothScrolling", 0);
                    await _registryService.SetRegistryValueAsync(edgeUserPath, "Enable_preload", 1);
                    await _registryService.SetRegistryValueAsync(edgeUserPath, "Enable_background_mode", 0);

                    _logger.Information("Applied Edge performance optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Edge performance optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Edge performance optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Office startup processes and disable unnecessary startup components
        /// </summary>
        private async Task<bool> ApplyOfficeStartupOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Office startup optimization");

                var registryPaths = new[]
                {
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Common\General",
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\15.0\Common\General"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Disable Office startup screen
                        await _registryService.SetRegistryValueAsync(registryPath, "ShownOptIn", 1);
                        await _registryService.SetRegistryValueAsync(registryPath, "ShownFirstRunOptin", 1);

                        // Disable Office telemetry
                        await _registryService.SetRegistryValueAsync(registryPath, "SendCustomerData", 0);

                        // Disable Office updates check on startup
                        await _registryService.SetRegistryValueAsync(registryPath, "NoUpdateCheck", 1);

                        _logger.Information("Applied Office startup optimization settings to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Office startup optimization to: {RegistryPath}", registryPath);
                    }
                }

                // Disable Office background processes
                var backgroundRegistryPath = @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Common\ClientTelemetry";
                try
                {
                    await _registryService.SetRegistryValueAsync(backgroundRegistryPath, "DisableTelemetry", 1);
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to disable Office background processes");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Office startup optimization");
                return false;
            }
        }

        /// <summary>
        /// Disable Office animations for better performance
        /// </summary>
        private async Task<bool> ApplyOfficeAnimationsDisableAsync()
        {
            try
            {
                _logger.Information("Starting Office animations disable optimization");

                var registryPaths = new[]
                {
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Common\Graphics",
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\15.0\Common\Graphics"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Disable Office animations
                        await _registryService.SetRegistryValueAsync(registryPath, "DisableAnimations", 1);

                        // Disable hardware acceleration if causing issues
                        await _registryService.SetRegistryValueAsync(registryPath, "DisableHardwareAcceleration", 0);

                        // Optimize graphics performance
                        await _registryService.SetRegistryValueAsync(registryPath, "OptimizeForPerformance", 1);

                        _logger.Information("Applied Office animations disable settings to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Office animations disable to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Office animations disable optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Office add-ins for better performance
        /// </summary>
        private async Task<bool> ApplyOfficeAddinsOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Office add-ins optimization");

                var registryPaths = new[]
                {
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Common\AddIns",
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\15.0\Common\AddIns"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Set add-in load behavior to load on demand
                        await _registryService.SetRegistryValueAsync(registryPath, "LoadBehavior", 9);

                        // Disable problematic add-ins
                        var problematicAddins = new[]
                        {
                            "Adobe PDF Reader",
                            "Skype Meeting Add-in",
                            "OneNote Linked Notes"
                        };

                        foreach (var addin in problematicAddins)
                        {
                            var addinPath = $"{registryPath}\\{addin}";
                            await _registryService.SetRegistryValueAsync(addinPath, "LoadBehavior", 0);
                        }

                        _logger.Information("Applied Office add-ins optimization settings to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Office add-ins optimization to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Office add-ins optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Adobe application cache settings
        /// </summary>
        private Task<bool> ApplyAdobeCacheOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Adobe cache optimization");

                // Adobe cache directories to optimize
                var adobeCachePaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Adobe", "Common", "Media Cache Files"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Adobe", "Common", "Peak Files"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Adobe", "Common", "Essential Graphics"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Adobe", "Common", "Essential Graphics")
                };

                bool success = false;

                foreach (var cachePath in adobeCachePaths)
                {
                    try
                    {
                        if (Directory.Exists(cachePath))
                        {
                            var cacheSize = GetDirectorySize(cachePath);

                            // Clear old cache files (older than 30 days)
                            var files = Directory.GetFiles(cachePath, "*", SearchOption.AllDirectories);
                            var oldFiles = files.Where(f => File.GetLastWriteTime(f) < DateTime.Now.AddDays(-30));

                            foreach (var file in oldFiles)
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch
                                {
                                    // Continue if file is in use
                                }
                            }

                            _logger.Information("Optimized Adobe cache: {CacheSize} MB in {CachePath}", cacheSize / 1024 / 1024, cachePath);
                            success = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to optimize Adobe cache: {CachePath}", cachePath);
                    }
                }

                return Task.FromResult(success);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Adobe cache optimization");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Optimize Adobe memory usage settings
        /// </summary>
        private async Task<bool> ApplyAdobeMemoryOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Adobe memory optimization");

                var registryPath = @"HKEY_CURRENT_USER\SOFTWARE\Adobe\Adobe Photoshop\130.0";

                bool success = false;

                try
                {
                    // Set Adobe memory usage to 70% of available RAM
                    var totalMemoryGB = GC.GetTotalMemory(false) / 1024 / 1024 / 1024;
                    var optimalMemoryMB = (int)(totalMemoryGB * 0.7 * 1024);

                    await _registryService.SetRegistryValueAsync(registryPath, "MaxMemoryUsage", optimalMemoryMB);

                    // Enable GPU acceleration
                    await _registryService.SetRegistryValueAsync(registryPath, "UseOpenGL", 1);

                    // Set optimal history states
                    await _registryService.SetRegistryValueAsync(registryPath, "HistoryStates", 20);

                    _logger.Information("Applied Adobe memory optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Adobe memory optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Adobe memory optimization");
                return false;
            }
        }

        /// <summary>
        /// Clean Steam download cache and shader cache
        /// </summary>
        private async Task<bool> ApplySteamCacheCleanupAsync()
        {
            try
            {
                _logger.Information("Starting Steam cache cleanup optimization");

                var platforms = await _softwareDetectionService.DetectGamingPlatformsAsync();
                var steam = platforms.FirstOrDefault(p => p.Name.ToLowerInvariant().Contains("steam"));

                if (steam == null)
                {
                    _logger.Warning("Steam not detected for cache cleanup");
                    return false;
                }

                bool success = false;

                try
                {
                    // Clear Steam download cache
                    var downloadCachePath = Path.Combine(steam.InstallPath, "appcache");
                    if (Directory.Exists(downloadCachePath))
                    {
                        var cacheSize = GetDirectorySize(downloadCachePath);
                        await Task.Run(() => Directory.Delete(downloadCachePath, true));
                        _logger.Information("Cleared Steam download cache: {CacheSize} MB", cacheSize / 1024 / 1024);
                    }

                    // Clear Steam shader cache
                    var shaderCachePath = Path.Combine(steam.InstallPath, "steamapps", "shadercache");
                    if (Directory.Exists(shaderCachePath))
                    {
                        var cacheSize = GetDirectorySize(shaderCachePath);
                        await Task.Run(() => Directory.Delete(shaderCachePath, true));
                        _logger.Information("Cleared Steam shader cache: {CacheSize} MB", cacheSize / 1024 / 1024);
                    }

                    // Clear Steam web browser cache
                    var webCachePath = Path.Combine(steam.InstallPath, "config", "htmlcache");
                    if (Directory.Exists(webCachePath))
                    {
                        var cacheSize = GetDirectorySize(webCachePath);
                        await Task.Run(() => Directory.Delete(webCachePath, true));
                        _logger.Information("Cleared Steam web browser cache: {CacheSize} MB", cacheSize / 1024 / 1024);
                    }

                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to clear Steam cache");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Steam cache cleanup optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Steam startup behavior
        /// </summary>
        private async Task<bool> ApplySteamStartupOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Steam startup optimization");

                var registryPath = @"HKEY_CURRENT_USER\SOFTWARE\Valve\Steam";

                bool success = false;

                try
                {
                    // Disable Steam auto-launch
                    await _registryService.SetRegistryValueAsync(registryPath, "RunAtStartup", 0);

                    // Disable Steam overlay
                    await _registryService.SetRegistryValueAsync(registryPath, "InGameOverlay", 0);

                    // Disable Steam web browser
                    await _registryService.SetRegistryValueAsync(registryPath, "WebBrowserEnabled", 0);

                    // Optimize Steam friends list updates
                    await _registryService.SetRegistryValueAsync(registryPath, "FriendsListUpdateInterval", 30);

                    _logger.Information("Applied Steam startup optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Steam startup optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Steam startup optimization");
                return false;
            }
        }

        /// <summary>
        /// Clean Epic Games Launcher cache
        /// </summary>
        private Task<bool> ApplyEpicCacheCleanupAsync()
        {
            try
            {
                _logger.Information("Starting Epic Games cache cleanup optimization");

                var epicCachePaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "EpicGamesLauncher", "Saved", "Logs"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "EpicGamesLauncher", "Saved", "webcache"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "EpicGamesLauncher", "Saved", "Config")
                };

                bool success = false;

                foreach (var cachePath in epicCachePaths)
                {
                    try
                    {
                        if (Directory.Exists(cachePath))
                        {
                            var cacheSize = GetDirectorySize(cachePath);

                            // Clear old cache files
                            var files = Directory.GetFiles(cachePath, "*", SearchOption.AllDirectories);
                            var oldFiles = files.Where(f => File.GetLastWriteTime(f) < DateTime.Now.AddDays(-7));

                            foreach (var file in oldFiles)
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch
                                {
                                    // Continue if file is in use
                                }
                            }

                            _logger.Information("Cleaned Epic Games cache: {CacheSize} MB in {CachePath}", cacheSize / 1024 / 1024, cachePath);
                            success = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to clean Epic Games cache: {CachePath}", cachePath);
                    }
                }

                return Task.FromResult(success);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Epic Games cache cleanup optimization");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Optimize Origin background processes
        /// </summary>
        private async Task<bool> ApplyOriginBackgroundOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Origin background optimization");

                var registryPath = @"HKEY_CURRENT_USER\SOFTWARE\Electronic Arts\EA Desktop";

                bool success = false;

                try
                {
                    // Disable Origin auto-launch
                    await _registryService.SetRegistryValueAsync(registryPath, "AutoLaunch", 0);

                    // Disable Origin background services
                    await _registryService.SetRegistryValueAsync(registryPath, "KeepClientRunning", 0);

                    // Disable Origin overlay
                    await _registryService.SetRegistryValueAsync(registryPath, "OverlayEnabled", 0);

                    // Optimize Origin update checking
                    await _registryService.SetRegistryValueAsync(registryPath, "AutoUpdateEnabled", 0);

                    _logger.Information("Applied Origin background optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Origin background optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Origin background optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Visual Studio IntelliSense for better performance
        /// </summary>
        private async Task<bool> ApplyVSIntelliSenseOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Visual Studio IntelliSense optimization");

                var registryPaths = new[]
                {
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\VisualStudio\16.0\Text Editor",
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\VisualStudio\17.0\Text Editor"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Optimize IntelliSense performance
                        await _registryService.SetRegistryValueAsync(registryPath, "EnableIntelliSensePreload", 1);
                        await _registryService.SetRegistryValueAsync(registryPath, "IntelliSenseMaxItems", 100);
                        await _registryService.SetRegistryValueAsync(registryPath, "IntelliSenseTimeout", 3000);

                        // Disable unnecessary language services
                        await _registryService.SetRegistryValueAsync(registryPath, "DisableUnnecessaryLanguageServices", 1);

                        // Optimize code analysis
                        await _registryService.SetRegistryValueAsync(registryPath, "EnableCodeAnalysis", 0);

                        _logger.Information("Applied Visual Studio IntelliSense optimization to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Visual Studio IntelliSense optimization to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Visual Studio IntelliSense optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Visual Studio extensions and add-ins
        /// </summary>
        private async Task<bool> ApplyVSExtensionsOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Visual Studio extensions optimization");

                var registryPaths = new[]
                {
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\VisualStudio\16.0\Extensions",
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\VisualStudio\17.0\Extensions"
                };

                bool success = false;

                foreach (var registryPath in registryPaths)
                {
                    try
                    {
                        // Optimize extension loading
                        await _registryService.SetRegistryValueAsync(registryPath, "LoadExtensionsOnDemand", 1);
                        await _registryService.SetRegistryValueAsync(registryPath, "ExtensionLoadTimeout", 5000);

                        // Disable auto-updates
                        await _registryService.SetRegistryValueAsync(registryPath, "AutoUpdateExtensions", 0);

                        // Disable problematic extensions
                        await _registryService.SetRegistryValueAsync(registryPath, "DisableGitExtensions", 1);

                        _logger.Information("Applied Visual Studio extensions optimization to: {RegistryPath}", registryPath);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply Visual Studio extensions optimization to: {RegistryPath}", registryPath);
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Visual Studio extensions optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize VS Code extensions for better performance
        /// </summary>
        private async Task<bool> ApplyVSCodeExtensionsOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting VS Code extensions optimization");

                var vscodeSettingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Code", "User", "settings.json");

                if (!File.Exists(vscodeSettingsPath))
                {
                    _logger.Warning("VS Code settings.json not found");
                    return false;
                }

                bool success = false;

                try
                {
                    // Add performance optimization settings
                    var optimizationSettings = new[]
                    {
                        "\"extensions.autoUpdate\": false,",
                        "\"extensions.autoCheckUpdates\": false,",
                        "\"extensions.showRecommendationsOnlyOnDemand\": true,",
                        "\"editor.quickSuggestionsDelay\": 100,",
                        "\"editor.suggest.maxVisibleSuggestions\": 10,",
                        "\"files.watcherExclude\": { \"**/node_modules/**\": true, \"**/.git/**\": true },",
                        "\"search.exclude\": { \"**/node_modules\": true, \"**/bower_components\": true }"
                    };

                    // Append optimization settings
                    var newSettings = string.Join(Environment.NewLine, optimizationSettings);
                    await File.AppendAllTextAsync(vscodeSettingsPath, newSettings);

                    _logger.Information("Applied VS Code extensions optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply VS Code extensions optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during VS Code extensions optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize VS Code settings for better performance
        /// </summary>
        private async Task<bool> ApplyVSCodeSettingsOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting VS Code settings optimization");

                var vscodeSettingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Code", "User", "settings.json");

                if (!File.Exists(vscodeSettingsPath))
                {
                    _logger.Warning("VS Code settings.json not found");
                    return false;
                }

                bool success = false;

                try
                {
                    // Add performance optimization settings
                    var performanceSettings = new[]
                    {
                        "\"editor.renderLineHighlight\": \"none\",",
                        "\"editor.renderWhitespace\": \"none\",",
                        "\"editor.minimap.enabled\": false,",
                        "\"editor.wordWrap\": \"off\",",
                        "\"editor.smoothScrolling\": false,",
                        "\"workbench.enableExperiments\": false,",
                        "\"workbench.settings.enableNaturalLanguageSearch\": false,",
                        "\"telemetry.enableTelemetry\": false,",
                        "\"telemetry.enableCrashReporter\": false"
                    };

                    // Append performance settings
                    var newSettings = string.Join(Environment.NewLine, performanceSettings);
                    await File.AppendAllTextAsync(vscodeSettingsPath, newSettings);

                    _logger.Information("Applied VS Code settings optimization");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply VS Code settings optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during VS Code settings optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize JetBrains IDE JVM settings
        /// </summary>
        private async Task<bool> ApplyJetBrainsJVMOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting JetBrains JVM optimization");

                var jetbrainsConfigPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "JetBrains");

                if (!Directory.Exists(jetbrainsConfigPath))
                {
                    _logger.Warning("JetBrains configuration directory not found");
                    return false;
                }

                bool success = false;

                try
                {
                    // Find JetBrains IDE configuration directories
                    var ideDirectories = Directory.GetDirectories(jetbrainsConfigPath, "*", SearchOption.TopDirectoryOnly);

                    foreach (var ideDir in ideDirectories)
                    {
                        var vmOptionsFiles = Directory.GetFiles(ideDir, "*.vmoptions", SearchOption.AllDirectories);

                        foreach (var vmOptionsFile in vmOptionsFiles)
                        {
                            // JVM optimization settings
                            var jvmSettings = new[]
                            {
                                "-Xms2g",
                                "-Xmx4g",
                                "-XX:+UseG1GC",
                                "-XX:+UseStringDeduplication",
                                "-XX:+OptimizeStringConcat",
                                "-Dsun.io.useCanonCaches=false",
                                "-Djava.net.preferIPv4Stack=true",
                                "-XX:+HeapDumpOnOutOfMemoryError"
                            };

                            await File.AppendAllLinesAsync(vmOptionsFile, jvmSettings);
                            _logger.Information("Applied JetBrains JVM optimization to: {VmOptionsFile}", vmOptionsFile);
                        }
                    }

                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply JetBrains JVM optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during JetBrains JVM optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Windows Search indexing for better performance
        /// </summary>
        private async Task<bool> ApplyWindowsSearchOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Windows Search optimization");

                var registryPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows Search";

                bool success = false;

                try
                {
                    // Optimize search indexing
                    await _registryService.SetRegistryValueAsync(registryPath, "EnableIndexing", 1);
                    await _registryService.SetRegistryValueAsync(registryPath, "IndexingBehavior", 1);

                    // Disable search in start menu for better performance
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Search",
                        "SearchboxTaskbarMode", 0);

                    _logger.Information("Applied Windows Search optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Windows Search optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Windows Search optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Windows Defender for better performance
        /// </summary>
        private async Task<bool> ApplyWindowsDefenderOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Windows Defender optimization");

                var registryPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows Defender";

                bool success = false;

                try
                {
                    // Optimize real-time protection settings
                    await _registryService.SetRegistryValueAsync(registryPath + @"\Real-Time Protection",
                        "DisableBehaviorMonitoring", 0);
                    await _registryService.SetRegistryValueAsync(registryPath + @"\Real-Time Protection",
                        "DisableOnAccessProtection", 0);

                    // Optimize scanning exclusions
                    await _registryService.SetRegistryValueAsync(registryPath + @"\Exclusions",
                        "Exclusions_TempPaths", 1);

                    _logger.Information("Applied Windows Defender optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Windows Defender optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Windows Defender optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Windows Update settings for better performance
        /// </summary>
        private async Task<bool> ApplyWindowsUpdateOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Windows Update optimization");

                var registryPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update";

                bool success = false;

                try
                {
                    // Set updates to download but not install automatically
                    await _registryService.SetRegistryValueAsync(registryPath, "AUOptions", 3);

                    // Disable automatic restart
                    await _registryService.SetRegistryValueAsync(registryPath, "NoAutoRebootWithLoggedOnUsers", 1);

                    // Optimize delivery optimization
                    await _registryService.SetRegistryValueAsync(@"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\DeliveryOptimization\Config",
                        "DODownloadMode", 1);

                    _logger.Information("Applied Windows Update optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Windows Update optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Windows Update optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Windows telemetry settings for privacy and performance
        /// </summary>
        private async Task<bool> ApplyWindowsTelemetryOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Windows Telemetry optimization");

                bool success = false;

                try
                {
                    // Disable telemetry data collection
                    await _registryService.SetRegistryValueAsync(@"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection",
                        "AllowTelemetry", 0);

                    // Disable advertising ID
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\AdvertisingInfo",
                        "Enabled", 0);

                    // Disable location tracking
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\DeviceAccess\Global\{BFA794E4-F964-4FDB-90F6-51056BFE4B44}",
                        "Value", "Deny");

                    _logger.Information("Applied Windows Telemetry optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Windows Telemetry optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Windows Telemetry optimization");
                return false;
            }
        }

        /// <summary>
        /// Optimize Windows notification settings for better performance
        /// </summary>
        private async Task<bool> ApplyWindowsNotificationsOptimizeAsync()
        {
            try
            {
                _logger.Information("Starting Windows Notifications optimization");

                bool success = false;

                try
                {
                    // Disable unnecessary notifications
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Notifications\Settings",
                        "NOC_GLOBAL_SETTING_ALLOW_NOTIFICATION_SOUND", 0);

                    // Disable tips and suggestions
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\ContentDeliveryManager",
                        "SoftLandingEnabled", 0);

                    // Disable welcome experience
                    await _registryService.SetRegistryValueAsync(@"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\ContentDeliveryManager",
                        "SubscribedContent-310093Enabled", 0);

                    _logger.Information("Applied Windows Notifications optimization settings");
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to apply Windows Notifications optimization");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during Windows Notifications optimization");
                return false;
            }
        }

        /// <summary>
        /// Helper method to get directory size in bytes
        /// </summary>
        private static long GetDirectorySize(string directoryPath)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return 0;

                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                long totalSize = 0;

                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        totalSize += fileInfo.Length;
                    }
                    catch
                    {
                        // Skip files that can't be accessed
                    }
                }

                return totalSize;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<List<OptimizationItem>> GetAllHardwareOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();

                // Get current system info for smart recommendations, but show all options
                var systemInfo = await _hardwareDetectionService.DetectHardwareAsync();

                // Show ALL Intel optimizations (user can see what's available for Intel systems)
                optimizations.AddRange(GetAllIntelOptimizations());

                // Show ALL AMD optimizations (user can see what's available for AMD systems)
                optimizations.AddRange(GetAllAmdOptimizations());

                // Show ALL storage optimizations (user can see all storage options)
                optimizations.AddRange(GetAllStorageOptimizations());

                // Show ALL memory optimizations (user can see all memory options)
                optimizations.AddRange(GetAllMemoryOptimizations());

                // Show ALL GPU optimizations (user can see all GPU options)
                optimizations.AddRange(GetAllGpuOptimizations());

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting all hardware optimizations");
                return new List<OptimizationItem>();
            }
        }

        private static List<OptimizationItem> GetAllIntelOptimizations()
        {
            var optimizations = new List<OptimizationItem>();

            // Intel Turbo Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_turbo_boost_optimize",
                Name = "Intel Turbo Boost Optimization",
                Description = "Optimize Intel Turbo Boost settings for better performance (Intel CPUs only)",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-15% CPU performance boost during demanding tasks"
            });

            // Intel SpeedStep optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_speedstep_optimize",
                Name = "Intel SpeedStep Power Management",
                Description = "Optimize Intel SpeedStep for balanced performance and power efficiency (Intel CPUs only)",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Better power efficiency with maintained performance"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetAllAmdOptimizations()
        {
            var optimizations = new List<OptimizationItem>();

            // AMD Precision Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_precision_boost_optimize",
                Name = "AMD Precision Boost Optimization",
                Description = "Optimize AMD Precision Boost for maximum performance (AMD CPUs only)",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-20% CPU performance improvement in multi-threaded workloads"
            });

            // AMD Cool'n'Quiet optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_coolnquiet_optimize",
                Name = "AMD Cool'n'Quiet Power Management",
                Description = "Optimize AMD Cool'n'Quiet for better thermal management (AMD CPUs only)",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Lower temperatures and better power efficiency"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetAllStorageOptimizations()
        {
            var optimizations = new List<OptimizationItem>();

            // NVMe-specific optimizations
            optimizations.Add(new OptimizationItem
            {
                Id = "nvme_power_management_optimize",
                Name = "NVMe Power Management Optimization",
                Description = "Optimize NVMe drive power management for maximum performance (NVMe drives only)",
                Category = "Storage",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "10-20% faster NVMe drive performance"
            });

            // SSD-specific optimizations
            optimizations.Add(new OptimizationItem
            {
                Id = "ssd_write_cache_optimize",
                Name = "SSD Write Cache Optimization",
                Description = "Optimize SSD write caching for better performance and longevity (SSD drives only)",
                Category = "Storage",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.MostlySafe,
                IsReversible = true,
                ExpectedImprovement = "15-25% faster write operations"
            });

            // HDD-specific optimizations
            optimizations.Add(new OptimizationItem
            {
                Id = "hdd_defragmentation_schedule",
                Name = "HDD Defragmentation Scheduling",
                Description = "Optimize defragmentation schedule for traditional hard drives (HDD drives only)",
                Category = "Storage",
                Impact = OptimizationImpact.High,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "20-40% faster file access on fragmented drives"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetAllMemoryOptimizations()
        {
            var optimizations = new List<OptimizationItem>();

            // High memory optimizations (16GB+)
            optimizations.Add(new OptimizationItem
            {
                Id = "high_memory_virtual_memory_disable",
                Name = "Disable Virtual Memory (High RAM)",
                Description = "Disable virtual memory for systems with 16GB+ RAM for better performance (16GB+ RAM recommended)",
                Category = "Memory",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Risky,
                IsReversible = true,
                ExpectedImprovement = "5-10% performance boost, faster application loading"
            });

            // Memory compression optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "memory_compression_optimize",
                Name = "Memory Compression Optimization",
                Description = "Optimize Windows memory compression for better RAM utilization (8GB+ RAM recommended)",
                Category = "Memory",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Better memory efficiency, reduced paging"
            });

            return optimizations;
        }

        private static List<OptimizationItem> GetAllGpuOptimizations()
        {
            var optimizations = new List<OptimizationItem>();

            // NVIDIA-specific optimizations
            optimizations.Add(new OptimizationItem
            {
                Id = "nvidia_gpu_scheduling_optimize",
                Name = "NVIDIA GPU Scheduling Optimization",
                Description = "Enable hardware-accelerated GPU scheduling for NVIDIA cards (NVIDIA GPUs only)",
                Category = "Graphics",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-15% gaming performance improvement"
            });

            // AMD GPU-specific optimizations
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_gpu_power_optimize",
                Name = "AMD GPU Power Management",
                Description = "Optimize AMD GPU power management for better performance (AMD GPUs only)",
                Category = "Graphics",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "10-20% better GPU performance and efficiency"
            });

            return optimizations;
        }

        /// <summary>
        /// Gets ALL software optimizations instantly without detection (for fast UI loading).
        /// Shows all available optimizations for user selection regardless of installation status.
        /// </summary>
        /// <returns>List of all software optimizations available</returns>
        private async Task<List<OptimizationItem>> GetAllSoftwareOptimizationsWithoutDetectionAsync()
        {
            try
            {
                _logger.Information("Getting all software optimizations without detection for instant UI loading");
                var optimizations = new List<OptimizationItem>();

                // Add ALL browser-specific optimizations (instant)
                var browserOptimizations = await GetBrowserSpecificOptimizationsAsync();
                optimizations.AddRange(browserOptimizations);

                // Add ALL office application optimizations (instant)
                var officeOptimizations = await GetOfficeSpecificOptimizationsAsync();
                optimizations.AddRange(officeOptimizations);

                // Add ALL gaming platform optimizations (instant)
                var gamingOptimizations = await GetGamingSpecificOptimizationsAsync();
                optimizations.AddRange(gamingOptimizations);

                // Add ALL development tool optimizations (instant)
                var developmentOptimizations = await GetDevelopmentSpecificOptimizationsAsync();
                optimizations.AddRange(developmentOptimizations);

                _logger.Information("Generated {Count} software optimizations instantly (no detection)", optimizations.Count);
                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting software optimizations without detection");
                return new List<OptimizationItem>();
            }
        }
    }
}
