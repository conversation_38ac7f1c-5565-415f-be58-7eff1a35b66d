<Page x:Class="PCOptimizerApp.Views.LiveOptimizationPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Live Optimization"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="MainCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Margin" Value="20"/>
            <Setter Property="Padding" Value="30"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="12"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ExplanationCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="0,15,0,0"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style x:Key="ProgressItemStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="0,8,0,0"/>
            <Setter Property="Padding" Value="15"/>
        </Style>

        <!-- Enhanced Checklist Item Style with Animation -->
        <Style x:Key="ChecklistItemStyle" TargetType="StackPanel">
            <Setter Property="Opacity" Value="0.7"/>
            <Style.Triggers>
                <!-- Completed Item Animation -->
                <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               From="0.7" To="1.0" Duration="0:0:0.5"/>
                                <ColorAnimation Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                              To="#27AE60" Duration="0:0:0.3"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Progress Bar Enhanced Style -->
        <Style x:Key="EnhancedProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="12"/>
            <Setter Property="Background" Value="#E9ECEF"/>
            <Setter Property="Foreground" Value="#3498DB"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                ClipToBounds="True">
                            <Rectangle x:Name="PART_Track"
                                     Fill="{TemplateBinding Foreground}"
                                     HorizontalAlignment="Left">
                                <Rectangle.RenderTransform>
                                    <TranslateTransform x:Name="ProgressTransform"/>
                                </Rectangle.RenderTransform>
                            </Rectangle>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsIndeterminate" Value="False">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ProgressTransform"
                                                           Storyboard.TargetProperty="X"
                                                           Duration="0:0:0.3"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Start Button Style -->
        <Style x:Key="StartButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" 
                                Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}"
                                BorderThickness="0">
                            <ContentPresenter x:Name="ButtonContent" 
                                            HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#219A52"/>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#1E8449"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Pause/Resume Button Style -->
        <Style x:Key="PauseResumeButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" 
                                Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}"
                                BorderThickness="0">
                            <ContentPresenter x:Name="ButtonContent" 
                                            HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#D35400"/>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#BA4A00"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="MainOptimizationTitle" Text="Live Optimization in Progress" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock x:Name="OptimizationProgressCountText" Text="Preparing for optimization..." FontSize="18" FontWeight="SemiBold" Foreground="#3498DB" Margin="0,8,0,0"/>
                    <TextBlock Text="Watch your PC get optimized in real-time with detailed explanations" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Name="OverallProgressText" Text="0%" FontSize="24" FontWeight="Bold"
                               Foreground="#27AE60" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button Name="StartOptimizationButton" Content="Start Optimization"
                            Background="#27AE60" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="15,8" BorderThickness="0" Click="StartOptimizationButton_Click" 
                            Margin="0,0,10,0" Style="{StaticResource StartButtonStyle}"/>
                    <Button Name="PauseResumeButton" Content="Pause"
                            Background="#E67E22" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="15,8" BorderThickness="0" Click="PauseResumeButton_Click" 
                            Visibility="Collapsed" Style="{StaticResource PauseResumeButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content with Improved Two-Column Layout -->
        <Grid Grid.Row="1" Margin="10,0">
            <Grid.ColumnDefinitions>
                <!-- Left Column: Expanded main content (75%) -->
                <ColumnDefinition Width="3*"/>
                <!-- Right Column: Action checklist (25%) -->
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Column: Enhanced Optimization Details with Better Readability -->
            <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" Margin="0,0,15,0">
                <StackPanel>
                    <!-- Current Optimization Card - Expanded for Better Readability -->
                    <Border Style="{StaticResource MainCardStyle}" Margin="10">
                        <StackPanel>
                            <Grid Margin="0,0,0,30">
                                <StackPanel>
                                    <!-- Reduced font size to prevent truncation -->
                                    <TextBlock Name="CurrentOptimizationTitle" Text="Enabling SSD TRIM"
                                               FontSize="28" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,12"
                                               TextWrapping="Wrap"/>

                                    <!-- Progress Counter - More Prominent -->
                                    <TextBlock Text="Step 4 of 8"
                                               FontSize="14" FontWeight="SemiBold" Foreground="#6C757D" 
                                               Margin="0,0,0,8"/>

                                    <!-- Current Status with Better Typography -->
                                    <TextBlock Name="CurrentOptimizationStatus" Text="Configuring storage optimization..."
                                               FontSize="18" Foreground="#495057" TextWrapping="Wrap" 
                                               LineHeight="26" Margin="0,0,0,20" MaxWidth="650"
                                               FontWeight="Normal"/>

                                    <!-- Progress Bar - Enhanced and More Prominent -->
                                    <Grid Margin="0,0,0,25">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <ProgressBar Grid.Column="0" Name="CurrentOptimizationProgress" 
                                                     Style="{StaticResource EnhancedProgressBarStyle}" 
                                                     Height="20" Margin="0,0,15,0"/>
                                        <TextBlock Grid.Column="1" Text="50%" 
                                                   FontSize="16" FontWeight="Bold" Foreground="#2C3E50"
                                                   VerticalAlignment="Center"/>
                                    </Grid>
                                </StackPanel>
                            </Grid>

                            <!-- Detailed Information Sections - Improved Layout and Typography -->
                            <StackPanel Name="DetailedStatusPanel">
                                <!-- Expert Analysis Section - Enhanced Readability -->
                                <Border Background="#F8F9FA" CornerRadius="8" Padding="18" Margin="0,0,0,12"
                                        Name="ExpertAnalysisSection" Visibility="Collapsed">
                                    <StackPanel>
                                        <TextBlock Text="Expert Analysis" FontSize="18" FontWeight="Bold"
                                                   Foreground="#495057" Margin="0,0,0,10"/>
                                        <TextBlock Name="ExpertAnalysisText" TextWrapping="Wrap" FontSize="15"
                                                   Foreground="#495057" LineHeight="24" 
                                                   FontWeight="Normal" TextAlignment="Left"/>
                                    </StackPanel>
                                </Border>

                                <!-- Technical Details Section - Enhanced Readability -->
                                <Border Background="#E8F4FD" CornerRadius="8" Padding="18" Margin="0,0,0,12"
                                        Name="TechnicalDetailsSection" Visibility="Collapsed">
                                    <StackPanel>
                                        <TextBlock Text="Technical Implementation" FontSize="18" FontWeight="Bold"
                                                   Foreground="#0D6EFD" Margin="0,0,0,10"/>
                                        <TextBlock Name="TechnicalDetailsText" TextWrapping="Wrap" FontSize="15"
                                                   Foreground="#084298" LineHeight="24"
                                                   FontWeight="Normal" TextAlignment="Left"/>
                                    </StackPanel>
                                </Border>

                                <!-- Expected Impact Section - Enhanced Readability -->
                                <Border Background="#D1E7DD" CornerRadius="8" Padding="18" Margin="0,0,0,12"
                                        Name="ExpectedImpactSection" Visibility="Collapsed">
                                    <StackPanel>
                                        <TextBlock Text="Expected Performance Impact" FontSize="18" FontWeight="Bold"
                                                   Foreground="#0F5132" Margin="0,0,0,10"/>
                                        <TextBlock Name="ExpectedImpactText" TextWrapping="Wrap" FontSize="15"
                                                   Foreground="#0A3622" LineHeight="24" 
                                                   FontWeight="Normal" TextAlignment="Left"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Progress History - More Compact -->
                    <Border Style="{StaticResource MainCardStyle}" Margin="10">
                        <StackPanel>
                            <TextBlock Text="Progress History" FontSize="18" FontWeight="Bold" 
                                       Foreground="#2C3E50" Margin="0,0,0,12"/>
                            
                            <ScrollViewer Name="ProgressHistoryScroll" MaxHeight="150" VerticalScrollBarVisibility="Auto">
                                <ItemsControl Name="ProgressHistoryList">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource ProgressItemStyle}">
                                                <StackPanel>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" FontSize="12"
                                                                   Margin="0,0,6,0" VerticalAlignment="Top"/>
                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                                                            <TextBlock Text="{Binding Impact}" FontSize="10"
                                                                       Foreground="#27AE60" Margin="0,1,0,0"/>
                                                        </StackPanel>
                                                    </Grid>
                                                    <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" FontSize="9"
                                                               Foreground="#BDC3C7" HorizontalAlignment="Right"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>

                    <!-- Value Communication -->
                    <Border Style="{StaticResource MainCardStyle}" Margin="10">
                        <StackPanel>
                            <TextBlock Text="Value Delivered" FontSize="20" FontWeight="Bold" 
                                       Foreground="#2C3E50" Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                    <TextBlock Name="MoneyValueText" Text="$0" FontSize="18" FontWeight="Bold" 
                                               Foreground="#27AE60"/>
                                    <TextBlock Text="Equivalent PC Upgrade Value" FontSize="10" Foreground="#7F8C8D" 
                                               TextWrapping="Wrap" HorizontalAlignment="Center"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                    <TextBlock Name="TimeSavedText" Text="0 min" FontSize="18" FontWeight="Bold" 
                                               Foreground="#3498DB"/>
                                    <TextBlock Text="Time Saved Per Week" FontSize="10" Foreground="#7F8C8D" 
                                               TextWrapping="Wrap" HorizontalAlignment="Center"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                    <TextBlock Name="OptimizationsAppliedText" Text="0" FontSize="18" FontWeight="Bold" 
                                               Foreground="#E74C3C"/>
                                    <TextBlock Text="Professional Optimizations" FontSize="10" Foreground="#7F8C8D" 
                                               TextWrapping="Wrap" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>


            <!-- Right Column: Action Checklist and Performance Metrics - Improved Layout -->
            <StackPanel Grid.Column="1" Margin="8,10,10,10">
                <!-- Action Checklist - Enhanced Design -->
                <Border Style="{StaticResource MainCardStyle}" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="Action Checklist" FontSize="16" FontWeight="Bold"
                                   Foreground="#2C3E50" Margin="0,0,0,15" HorizontalAlignment="Center"/>
                        
                        <!-- Checklist Progress Indicator - Enhanced -->
                        <Border Background="#E9ECEF" CornerRadius="8" Padding="12" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Name="ChecklistProgressText" Text="Ready to start optimization"
                                           FontSize="13" FontWeight="SemiBold" Foreground="#495057" 
                                           HorizontalAlignment="Center" Margin="0,0,0,8" TextWrapping="Wrap"/>
                                <ProgressBar Name="ChecklistProgressBar" Height="10" Background="#DEE2E6"
                                             Foreground="#28A745" Value="0" Maximum="100"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Checklist Items - Better Alignment and Spacing -->
                        <ScrollViewer MaxHeight="280" VerticalScrollBarVisibility="Auto">
                            <ItemsControl Name="ChecklistItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" CornerRadius="6" Padding="12" Margin="0,0,0,8"
                                                BorderBrush="#E9ECEF" BorderThickness="1">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <!-- Status Icon - Better Alignment -->
                                                <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" FontSize="14" FontWeight="Bold"
                                                           Margin="0,0,10,0" VerticalAlignment="Top">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                                    <Setter Property="Foreground" Value="#28A745"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsCompleted}" Value="False">
                                                                    <Setter Property="Foreground" Value="#ADB5BD"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                                
                                                <!-- Description and Status -->
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Description}" FontSize="12" FontWeight="SemiBold"
                                                               TextWrapping="Wrap" Margin="0,0,0,4">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="Foreground" Value="#495057"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                                        <Setter Property="Foreground" Value="#28A745"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                    
                                                    <!-- Category and Status -->
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="{Binding Category}" FontSize="9" 
                                                                   Background="#E9ECEF" Foreground="#6C757D"
                                                                   Padding="4,2" FontWeight="SemiBold"/>
                                                        <TextBlock Text="{Binding StatusText}" FontSize="10" 
                                                                   Foreground="#6C757D" Margin="8,0,0,0" 
                                                                   VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                        
                        <!-- Checklist Summary - Enhanced -->
                        <Border Background="#D1E7DD" CornerRadius="8" Padding="12" Margin="0,15,0,0">
                            <StackPanel>
                                <TextBlock Name="ChecklistSummaryText" Text="0 of 0 actions completed"
                                           FontSize="12" FontWeight="Bold" Foreground="#0F5132" 
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="Real-time progress tracking"
                                           FontSize="10" Foreground="#0A3622" 
                                           HorizontalAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Performance Metrics - Enhanced Design -->
                <Border Style="{StaticResource MainCardStyle}">
                    <StackPanel>
                        <TextBlock Text="Performance Impact" FontSize="16" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15" HorizontalAlignment="Center"/>
                        <StackPanel Margin="5,0">
                            <!-- Boot Time -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Boot Time" FontSize="11" Foreground="#7F8C8D" FontWeight="SemiBold"/>
                                    <TextBlock Name="BootTimeValue" Text="45s" FontSize="18" FontWeight="Bold" Foreground="#2C3E50"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Name="BootTimeChange" Text="-12s" FontSize="12" FontWeight="Bold" 
                                           Foreground="#27AE60" VerticalAlignment="Center"/>
                            </Grid>

                            <!-- App Load Time -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="App Load" FontSize="11" Foreground="#7F8C8D" FontWeight="SemiBold"/>
                                    <TextBlock Name="AppLoadValue" Text="3.2s" FontSize="18" FontWeight="Bold" Foreground="#2C3E50"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Name="AppLoadChange" Text="-0.8s" FontSize="12" FontWeight="Bold" 
                                           Foreground="#27AE60" VerticalAlignment="Center"/>
                            </Grid>

                            <!-- Memory Efficiency -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Memory Usage" FontSize="11" Foreground="#7F8C8D" FontWeight="SemiBold"/>
                                    <TextBlock Name="MemoryEfficiencyValue" Text="72%" FontSize="18" FontWeight="Bold" Foreground="#2C3E50"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Name="MemoryEfficiencyChange" Text="-15%" FontSize="12" FontWeight="Bold" 
                                           Foreground="#27AE60" VerticalAlignment="Center"/>
                            </Grid>

                            <!-- Disk Performance -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Disk Speed" FontSize="11" Foreground="#7F8C8D" FontWeight="SemiBold"/>
                                    <TextBlock Name="DiskPerformanceValue" Text="450MB/s" FontSize="18" FontWeight="Bold" Foreground="#2C3E50"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Name="DiskPerformanceChange" Text="+150MB/s" FontSize="12" FontWeight="Bold" 
                                           Foreground="#27AE60" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
