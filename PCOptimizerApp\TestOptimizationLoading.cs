using System;
using System.Threading.Tasks;
using PCOptimizerApp.Services;
using Serilog;

namespace PCOptimizerApp
{
    /// <summary>
    /// Simple test class to verify optimization loading works correctly
    /// </summary>
    public class TestOptimizationLoading
    {
        public static async Task Main(string[] args)
        {
            // Configure basic logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            try
            {
                Console.WriteLine("Testing Optimization Loading...");
                
                // Create service instances
                var registryService = new RegistryService();
                var hardwareDetectionService = new HardwareDetectionService();
                var softwareDetectionService = new SoftwareDetectionService();
                var backupService = new BackupService();
                
                var optimizationService = new OptimizationService(
                    registryService, 
                    hardwareDetectionService, 
                    softwareDetectionService, 
                    backupService, 
                    Log.Logger);

                // Test GetBasicOptimizations
                Console.WriteLine("\n=== Testing GetBasicOptimizations ===");
                var basicOptimizations = optimizationService.GetType()
                    .GetMethod("GetBasicOptimizations", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                    ?.Invoke(null, null) as System.Collections.Generic.List<PCOptimizerApp.Models.OptimizationItem>;

                if (basicOptimizations != null)
                {
                    Console.WriteLine($"✅ GetBasicOptimizations returned {basicOptimizations.Count} optimizations");
                    
                    // Group by category to verify our new categories
                    var categoryGroups = basicOptimizations.GroupBy(o => o.Category).OrderBy(g => g.Key);
                    
                    Console.WriteLine("\n=== Categories Found ===");
                    foreach (var group in categoryGroups)
                    {
                        Console.WriteLine($"📁 {group.Key}: {group.Count()} optimizations");
                        
                        // Show first few optimizations in each category
                        foreach (var opt in group.Take(3))
                        {
                            Console.WriteLine($"   - {opt.Id}: {opt.Name}");
                        }
                        if (group.Count() > 3)
                        {
                            Console.WriteLine($"   ... and {group.Count() - 3} more");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("❌ GetBasicOptimizations returned null");
                }

                // Test GetAllOptimizationsAsync
                Console.WriteLine("\n=== Testing GetAllOptimizationsAsync ===");
                var allOptimizations = await optimizationService.GetAllOptimizationsAsync();
                
                if (allOptimizations != null)
                {
                    Console.WriteLine($"✅ GetAllOptimizationsAsync returned {allOptimizations.Count} optimizations");
                    
                    // Check for our new categories
                    var newCategories = new[] { "UI Responsiveness", "File Operations", "Window Management", "Memory & System", "Boot Acceleration" };
                    var foundNewCategories = allOptimizations
                        .Where(o => newCategories.Contains(o.Category))
                        .GroupBy(o => o.Category)
                        .ToList();
                    
                    Console.WriteLine("\n=== New Categories Verification ===");
                    foreach (var category in newCategories)
                    {
                        var found = foundNewCategories.FirstOrDefault(g => g.Key == category);
                        if (found != null)
                        {
                            Console.WriteLine($"✅ {category}: {found.Count()} optimizations found");
                        }
                        else
                        {
                            Console.WriteLine($"❌ {category}: NOT FOUND");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("❌ GetAllOptimizationsAsync returned null");
                }

                Console.WriteLine("\n=== Test Complete ===");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during testing: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
