using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PCOptimizerApp.Models;
using PCOptimizerApp.Controls;
using Serilog;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service for generating AI-powered explanations during optimizations
    /// </summary>
    public class AIExplanationService : IAIExplanationService
    {
        private static readonly ILogger Logger = Log.ForContext<AIExplanationService>();
        
        private readonly Dictionary<string, OptimizationExplanation> _explanations;

        public AIExplanationService()
        {
            _explanations = InitializeExplanations();
            Logger.Information("AI Explanation Service initialized with {Count} explanations", _explanations.Count);
        }

        public OptimizationExplanation GetExplanation(string optimizationId, SystemAnalysisContext context)
        {
            try
            {
                if (_explanations.TryGetValue(optimizationId, out var baseExplanation))
                {
                    return PersonalizeExplanation(baseExplanation, context);
                }

                Logger.Warning("No explanation found for optimization: {OptimizationId}", optimizationId);
                return GetGenericExplanation(optimizationId);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error generating explanation for optimization: {OptimizationId}", optimizationId);
                return GetGenericExplanation(optimizationId);
            }
        }

        private static OptimizationExplanation PersonalizeExplanation(OptimizationExplanation baseExplanation, SystemAnalysisContext context)
        {
            var personalizedExplanation = new OptimizationExplanation
            {
                OptimizationId = baseExplanation.OptimizationId,
                PreAnalysisText = PersonalizeText(baseExplanation.PreAnalysisText, context),
                AnalysisText = PersonalizeText(baseExplanation.AnalysisText, context),
                ActionText = PersonalizeText(baseExplanation.ActionText, context),
                CompletionText = PersonalizeText(baseExplanation.CompletionText, context),
                TypingSpeed = baseExplanation.TypingSpeed,
                ExpectedDuration = baseExplanation.ExpectedDuration
            };

            return personalizedExplanation;
        }

        private static string PersonalizeText(string template, SystemAnalysisContext context)
        {
            return template
                .Replace("{RAM_SIZE}", $"{context.TotalRamGB}GB")
                .Replace("{CPU_CORES}", context.CpuCores.ToString())
                .Replace("{OS_VERSION}", context.WindowsVersion)
                .Replace("{DISK_TYPE}", context.HasSsd ? "SSD" : "HDD")
                .Replace("{FILES_COUNT}", context.TempFilesCount.ToString())
                .Replace("{FILES_SIZE}", $"{context.TempFilesSizeGB:F1}GB")
                .Replace("{STARTUP_COUNT}", context.StartupProgramsCount.ToString())
                .Replace("{BOOT_TIME}", $"{context.CurrentBootTimeSeconds}")
                .Replace("{ESTIMATED_IMPROVEMENT}", $"{context.EstimatedBootTimeSeconds}")
                .Replace("{PERFORMANCE_GAIN}", $"{context.EstimatedPerformanceGain}%");
        }

        private static OptimizationExplanation GetGenericExplanation(string optimizationId)
        {
            return new OptimizationExplanation
            {
                OptimizationId = optimizationId,
                PreAnalysisText = "I'm preparing to analyze your system for this optimization.",
                AnalysisText = "I'm examining your system configuration and identifying potential improvements.",
                ActionText = "I'm now applying the optimization to improve your system's performance.",
                CompletionText = "The optimization has been successfully applied to your system.",
                TypingSpeed = TypingSpeedMode.Medium,
                ExpectedDuration = TimeSpan.FromSeconds(3)
            };
        }

        private static Dictionary<string, OptimizationExplanation> InitializeExplanations()
        {
            return new Dictionary<string, OptimizationExplanation>
            {
                ["visual_effects_performance"] = new()
                {
                    OptimizationId = "visual_effects_performance",
                    PreAnalysisText = "I'm examining your visual effects settings to find the optimal balance between appearance and performance.",
                    AnalysisText = "I've detected that Windows is prioritizing visual appearance over performance. With {RAM_SIZE} of RAM and your {DISK_TYPE} storage, I can optimize these settings for better responsiveness.",
                    ActionText = "I'm adjusting your visual effects to 'Adjust for best performance' mode. This will disable transparency effects, animations, and visual flourishes that consume system resources.",
                    CompletionText = "Visual effects optimization complete! You should notice a {PERFORMANCE_GAIN} improvement in system responsiveness, especially when opening windows and switching between applications.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(5)
                },

                ["startup_programs_cleanup"] = new()
                {
                    OptimizationId = "startup_programs_cleanup",
                    PreAnalysisText = "I'm analyzing your startup programs to identify which ones are impacting your boot time.",
                    AnalysisText = "I found {STARTUP_COUNT} programs launching at startup, consuming valuable system resources. Your current boot time is approximately {BOOT_TIME} seconds. I've identified several non-essential programs that can be safely disabled.",
                    ActionText = "I'm disabling non-critical startup programs including productivity software, updaters, and background utilities that aren't essential for system operation. Essential security and system programs will remain enabled.",
                    CompletionText = "Startup optimization complete! I've reduced your startup programs and your boot time should improve to approximately {ESTIMATED_IMPROVEMENT} seconds - that's a significant performance boost!",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(8)
                },

                ["temporary_files_cleanup"] = new()
                {
                    OptimizationId = "temporary_files_cleanup",
                    PreAnalysisText = "I'm scanning your system for temporary files, cache data, and other unnecessary files that can be safely removed.",
                    AnalysisText = "I've discovered {FILES_SIZE} of temporary files across {FILES_COUNT} files. These include browser cache, Windows temp files, application remnants, and system logs that are no longer needed.",
                    ActionText = "I'm carefully removing these temporary files while preserving important data. This includes clearing browser caches, Windows temporary directories, and application cache folders.",
                    CompletionText = "Cleanup complete! I've freed up {FILES_SIZE} of storage space. This will help improve system performance and give you more room for your important files.",
                    TypingSpeed = TypingSpeedMode.Fast,
                    ExpectedDuration = TimeSpan.FromSeconds(12)
                },

                ["power_high_performance"] = new()
                {
                    OptimizationId = "power_high_performance",
                    PreAnalysisText = "I'm analyzing your current power management settings to optimize for maximum performance.",
                    AnalysisText = "Your system is currently using a balanced power plan which prioritizes energy efficiency over performance. For optimal performance, especially during demanding tasks, I recommend switching to high performance mode.",
                    ActionText = "I'm configuring your power settings to 'High Performance' mode. This will prevent CPU throttling, keep components running at full speed, and minimize power-saving delays.",
                    CompletionText = "Power optimization complete! Your system is now configured for maximum performance. You'll notice improved responsiveness, especially during CPU-intensive tasks.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(4)
                },

                ["ssd_trim_enable"] = new()
                {
                    OptimizationId = "ssd_trim_enable",
                    PreAnalysisText = "I'm checking your {DISK_TYPE} configuration to ensure optimal performance and longevity.",
                    AnalysisText = "I've detected that you have an SSD drive. TRIM is essential for maintaining SSD performance over time by allowing the drive to efficiently manage deleted data blocks.",
                    ActionText = "I'm enabling TRIM functionality for your SSD. This will help maintain optimal write speeds and extend the lifespan of your solid-state drive.",
                    CompletionText = "SSD optimization complete! TRIM is now enabled, which will help maintain your SSD's performance and extend its operational lifespan.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(3)
                },

                ["ssd_superfetch_disable"] = new()
                {
                    OptimizationId = "ssd_superfetch_disable",
                    PreAnalysisText = "I'm analyzing your storage configuration to optimize for your {DISK_TYPE} drive.",
                    AnalysisText = "I've detected an SSD drive. Superfetch (SysMain) was designed for traditional hard drives and can actually reduce performance on SSDs by creating unnecessary read/write operations.",
                    ActionText = "I'm disabling Superfetch service since it's not beneficial for SSD drives. This will reduce unnecessary disk activity and improve overall system responsiveness.",
                    CompletionText = "SSD optimization complete! Superfetch has been disabled, reducing unnecessary disk operations and improving your SSD's efficiency.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(4)
                },

                ["gaming_mode_enable"] = new()
                {
                    OptimizationId = "gaming_mode_enable",
                    PreAnalysisText = "I'm configuring gaming optimizations to give you the best possible gaming experience.",
                    AnalysisText = "I'm enabling Windows Game Mode, which prioritizes gaming applications by allocating more CPU and GPU resources to games while reducing background processes.",
                    ActionText = "I'm activating Game Mode and optimizing related settings including GPU scheduling, game DVR settings, and process priorities to minimize latency and maximize gaming performance.",
                    CompletionText = "Gaming optimization complete! Game Mode is now active. You should experience better frame rates, reduced input lag, and improved overall gaming performance.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(6)
                },

                ["multicore_cpu_scheduling"] = new()
                {
                    OptimizationId = "multicore_cpu_scheduling",
                    PreAnalysisText = "I'm analyzing your {CPU_CORES}-core processor to optimize task scheduling and CPU utilization.",
                    AnalysisText = "I'm configuring Windows to better utilize all {CPU_CORES} cores of your processor. This involves optimizing thread scheduling and CPU affinity settings for improved multitasking performance.",
                    ActionText = "I'm updating CPU scheduling parameters to enhance multi-core utilization. This will help distribute workloads more efficiently across all processor cores.",
                    CompletionText = "Multi-core optimization complete! Your {CPU_CORES}-core processor is now better optimized for multitasking and parallel processing workloads.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(5)
                },

                ["memory_compression_optimize"] = new()
                {
                    OptimizationId = "memory_compression_optimize",
                    PreAnalysisText = "I'm analyzing your {RAM_SIZE} memory configuration to optimize memory usage efficiency.",
                    AnalysisText = "I'm enabling memory compression to make more efficient use of your {RAM_SIZE} of RAM. This feature compresses inactive memory pages, effectively increasing available memory without physical upgrades.",
                    ActionText = "I'm configuring Windows memory compression to optimize RAM utilization. This will help reduce memory pressure and improve system responsiveness when memory usage is high.",
                    CompletionText = "Memory optimization complete! Memory compression is now active, effectively increasing your usable RAM and improving overall system performance.",
                    TypingSpeed = TypingSpeedMode.Medium,
                    ExpectedDuration = TimeSpan.FromSeconds(4)
                }
            };
        }
    }

    public interface IAIExplanationService
    {
        OptimizationExplanation GetExplanation(string optimizationId, SystemAnalysisContext context);
    }
}
