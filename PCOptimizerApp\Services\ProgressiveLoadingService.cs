using PCOptimizerApp.Models;
using System.ComponentModel;

namespace PCOptimizerApp.Services
{
    public interface IProgressiveLoadingService
    {
        event EventHandler<SystemInfo>? SystemInfoLoaded;
        event EventHandler<SystemHealthScore>? HealthScoreLoaded; 
        event EventHandler<PerformanceMetrics>? PerformanceMetricsLoaded;
        event EventHandler<string>? LoadingProgressUpdated;
        event EventHandler<Exception>? LoadingErrorOccurred;

        Task StartProgressiveLoadingAsync();
        bool IsSystemInfoLoaded { get; }
        bool IsHealthScoreLoaded { get; }
        bool IsPerformanceMetricsLoaded { get; }
        bool IsFullyLoaded { get; }
    }

    public class ProgressiveLoadingService : IProgressiveLoadingService
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;

        public event EventHandler<SystemInfo>? SystemInfoLoaded;
        public event EventHandler<SystemHealthScore>? HealthScoreLoaded;
        public event EventHandler<PerformanceMetrics>? PerformanceMetricsLoaded;
        public event EventHandler<string>? LoadingProgressUpdated;
        public event EventHandler<Exception>? LoadingErrorOccurred;

        public bool IsSystemInfoLoaded { get; private set; }
        public bool IsHealthScoreLoaded { get; private set; }
        public bool IsPerformanceMetricsLoaded { get; private set; }
        public bool IsFullyLoaded => IsSystemInfoLoaded && IsHealthScoreLoaded && IsPerformanceMetricsLoaded;

        public ProgressiveLoadingService(
            ISystemInfoService systemInfoService,
            IPerformanceMonitoringService performanceMonitoringService)
        {
            _systemInfoService = systemInfoService;
            _performanceMonitoringService = performanceMonitoringService;
        }

        public async Task StartProgressiveLoadingAsync()
        {
            try
            {
                // Load system info first (most important)
                LoadingProgressUpdated?.Invoke(this, "Loading system information...");
                await LoadSystemInfoAsync();

                // Load performance metrics (quick)
                LoadingProgressUpdated?.Invoke(this, "Getting performance metrics...");
                await LoadPerformanceMetricsAsync();

                // Load health score last (can be slow)
                LoadingProgressUpdated?.Invoke(this, "Calculating system health score...");
                await LoadHealthScoreAsync();

                LoadingProgressUpdated?.Invoke(this, "Loading complete!");
            }
            catch (Exception ex)
            {
                LoadingErrorOccurred?.Invoke(this, ex);
            }
        }

        private async Task LoadSystemInfoAsync()
        {
            try
            {
                var systemInfo = await _systemInfoService.GetSystemInfoAsync();
                IsSystemInfoLoaded = true;
                SystemInfoLoaded?.Invoke(this, systemInfo);
            }
            catch (Exception ex)
            {
                LoadingErrorOccurred?.Invoke(this, new Exception("Failed to load system information", ex));
            }
        }

        private async Task LoadPerformanceMetricsAsync()
        {
            try
            {
                var metrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
                IsPerformanceMetricsLoaded = true;
                PerformanceMetricsLoaded?.Invoke(this, metrics);
            }
            catch (Exception ex)
            {
                LoadingErrorOccurred?.Invoke(this, new Exception("Failed to load performance metrics", ex));
            }
        }

        private async Task LoadHealthScoreAsync()
        {
            try
            {
                var healthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                IsHealthScoreLoaded = true;
                HealthScoreLoaded?.Invoke(this, healthScore);
            }
            catch (Exception ex)
            {
                LoadingErrorOccurred?.Invoke(this, new Exception("Failed to calculate health score", ex));
            }
        }
    }
}
