using System;
using System.Collections.Generic;
using System.Linq;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service to provide dynamic, context-aware status messages for the Live Optimization feature
    /// </summary>
    public class StatusMessageService
    {
        private readonly Random _random = new();

        // Category constants
        private const string STARTUP = "startup";
        private const string MEMORY = "memory";
        private const string STORAGE = "storage";
        private const string GRAPHICS = "graphics";
        private const string NETWORK = "network";
        private const string CPU = "cpu";
        private const string GENERAL = "general";

        // Dynamic status verbs for different phases
        private readonly Dictionary<OptimizationPhase, List<string>> _statusVerbs = new()
        {
            [OptimizationPhase.Analyzing] = new List<string>
            {
                "🔍 Analyzing", "🔎 Examining", "📊 Assessing", "🔬 Investigating", 
                "📋 Evaluating", "🎯 Scanning", "🔍 Diagnosing", "📈 Measuring",
                "🧠 Thinking", "💭 Considering", "🔍 Discovering", "📊 Profiling"
            },
            [OptimizationPhase.Processing] = new List<string>
            {
                "⚙️ Processing", "🔧 Configuring", "⚡ Optimizing", "🛠️ Adjusting",
                "🎛️ Tuning", "🔄 Refining", "⚙️ Calibrating", "🎯 Targeting",
                "🚀 Enhancing", "⚡ Boosting", "🔧 Fine-tuning", "🎚️ Balancing"
            },
            [OptimizationPhase.Applying] = new List<string>
            {
                "✅ Applying", "💾 Implementing", "🎯 Executing", "🚀 Deploying",
                "✨ Activating", "⚡ Engaging", "🔄 Installing", "💫 Enabling",
                "🎪 Orchestrating", "🎬 Directing", "🎯 Targeting", "✨ Manifesting"
            },
            [OptimizationPhase.Validating] = new List<string>
            {
                "✅ Validating", "🔍 Verifying", "🧪 Testing", "📊 Confirming",
                "🎯 Checking", "✅ Ensuring", "📋 Reviewing", "🔬 Inspecting",
                "🧾 Auditing", "📈 Monitoring", "🎪 Showcasing", "✨ Celebrating"
            },
            [OptimizationPhase.Finalizing] = new List<string>
            {
                "🏁 Finalizing", "✅ Completing", "🎉 Finishing", "🎯 Wrapping up",
                "💾 Saving", "📋 Documenting", "🎪 Concluding", "✨ Perfecting",
                "🚀 Launching", "🎬 Presenting", "🎊 Celebrating", "🏆 Achieving"
            }
        };

        // Context-aware thinking messages
        private readonly Dictionary<string, List<string>> _thinkingMessages = new()
        {
            [STARTUP] = new List<string>
            {
                "🧠 Thinking about which programs really need to start with Windows...",
                "💭 Considering startup impact on boot performance...",
                "🔍 Analyzing startup dependencies and priorities...",
                "📊 Evaluating startup time vs. functionality trade-offs...",
                "🎯 Identifying programs that can be delayed or disabled..."
            },
            [MEMORY] = new List<string>
            {
                "🧠 Thinking about optimal memory allocation strategies...",
                "💭 Considering virtual memory and paging file settings...",
                "🔍 Analyzing memory usage patterns and bottlenecks...",
                "📊 Evaluating RAM optimization opportunities...",
                "🎯 Planning memory compression and caching improvements..."
            },
            [STORAGE] = new List<string>
            {
                "🧠 Thinking about disk performance optimization techniques...",
                "💭 Considering SSD vs HDD specific optimizations...",
                "🔍 Analyzing file system efficiency and fragmentation...",
                "📊 Evaluating storage access patterns...",
                "🎯 Planning TRIM, caching, and indexing improvements..."
            },
            [GRAPHICS] = new List<string>
            {
                "🧠 Thinking about GPU performance vs. visual quality balance...",
                "💭 Considering graphics driver and hardware acceleration settings...",
                "🔍 Analyzing display and rendering optimization opportunities...",
                "📊 Evaluating frame rates and visual fidelity trade-offs...",
                "🎯 Planning GPU scheduling and memory optimizations..."
            },
            [NETWORK] = new List<string>
            {
                "🧠 Thinking about network throughput and latency improvements...",
                "💭 Considering TCP/IP stack optimizations...",
                "🔍 Analyzing network adapter settings and DNS configuration...",
                "📊 Evaluating bandwidth utilization patterns...",
                "🎯 Planning connection and protocol optimizations..."
            },
            [CPU] = new List<string>
            {
                "🧠 Thinking about processor performance and power management...",
                "💭 Considering CPU scheduling and core utilization...",
                "🔍 Analyzing thermal management and frequency scaling...",
                "📊 Evaluating processor efficiency and workload distribution...",
                "🎯 Planning power state and performance optimizations..."
            },
            [GENERAL] = new List<string>
            {
                "🧠 Thinking about this optimization carefully...",
                "💭 Considering the best approach for your system...",
                "🔍 Analyzing configuration options and impacts...",
                "📊 Evaluating performance improvement potential...",
                "🎯 Planning the most effective implementation strategy..."
            }
        };

        // Applying messages with more personality
        private readonly Dictionary<string, List<string>> _applyingMessages = new()
        {
            [STARTUP] = new List<string>
            {
                "🎯 Applying intelligent startup management - decluttering your boot process...",
                "⚡ Implementing startup optimizations - your PC will thank you later...",
                "🚀 Deploying startup improvements - faster boots incoming...",
                "✨ Activating startup efficiency protocols - streamlining system initialization...",
                "🔧 Fine-tuning startup sequence - prioritizing essential programs..."
            },
            [MEMORY] = new List<string>
            {
                "🎯 Applying advanced memory management - optimizing RAM utilization...",
                "⚡ Implementing memory efficiency protocols - better multitasking ahead...",
                "🚀 Deploying RAM optimizations - smoother performance loading...",
                "✨ Activating memory compression and caching - maximizing available resources...",
                "🔧 Fine-tuning virtual memory settings - balancing speed and stability..."
            },
            [STORAGE] = new List<string>
            {
                "🎯 Applying storage performance enhancements - maximizing disk efficiency...",
                "⚡ Implementing advanced disk optimizations - faster file operations coming...",
                "🚀 Deploying SSD/HDD performance tuning - optimizing for your hardware...",
                "✨ Activating intelligent caching and indexing - smarter storage management...",
                "🔧 Fine-tuning file system parameters - improving access patterns..."
            },
            [GRAPHICS] = new List<string>
            {
                "🎯 Applying graphics performance optimizations - balancing quality and speed...",
                "⚡ Implementing GPU efficiency protocols - smoother visual experience ahead...",
                "🚀 Deploying display and rendering improvements - optimizing frame delivery...",
                "✨ Activating hardware acceleration settings - leveraging your GPU's power...",
                "🔧 Fine-tuning graphics driver configuration - maximizing compatibility..."
            },
            [NETWORK] = new List<string>
            {
                "🎯 Applying network performance enhancements - faster internet ahead...",
                "⚡ Implementing connectivity optimizations - reducing latency and improving throughput...",
                "🚀 Deploying TCP/IP stack improvements - optimizing data transmission...",
                "✨ Activating intelligent network protocols - smarter connection management...",
                "🔧 Fine-tuning adapter settings and DNS - enhancing web browsing experience..."
            },
            [CPU] = new List<string>
            {
                "🎯 Applying processor performance tuning - optimizing CPU efficiency...",
                "⚡ Implementing advanced power management - balancing performance and energy...",
                "🚀 Deploying CPU scheduling optimizations - better task distribution...",
                "✨ Activating thermal management protocols - maintaining optimal temperatures...",
                "🔧 Fine-tuning frequency scaling and core parking - maximizing responsiveness..."
            },
            [GENERAL] = new List<string>
            {
                "🎯 Applying this optimization with precision...",
                "⚡ Implementing performance improvements intelligently...",
                "🚀 Deploying system enhancements strategically...",
                "✨ Activating optimization protocols effectively...",
                "🔧 Fine-tuning system parameters for optimal results..."
            }
        };

        /// <summary>
        /// Get a random status verb for the specified optimization phase
        /// </summary>
        public string GetStatusVerb(OptimizationPhase phase)
        {
            if (_statusVerbs.TryGetValue(phase, out var verbs) && verbs.Any())
            {
                return verbs[_random.Next(verbs.Count)];
            }
            return phase switch
            {
                OptimizationPhase.Analyzing => "🔍 Analyzing",
                OptimizationPhase.Processing => "⚙️ Processing",
                OptimizationPhase.Applying => "✅ Applying",
                OptimizationPhase.Validating => "✅ Validating",
                OptimizationPhase.Finalizing => "🏁 Finalizing",
                _ => "🔧 Working"
            };
        }

        /// <summary>
        /// Get a context-aware thinking message based on optimization category
        /// </summary>
        public string GetThinkingMessage(string optimizationCategory)
        {
            var category = GetCategoryKey(optimizationCategory);
            
            if (_thinkingMessages.TryGetValue(category, out var messages) && messages.Any())
            {
                return messages[_random.Next(messages.Count)];
            }
            
            return _thinkingMessages[GENERAL][_random.Next(_thinkingMessages[GENERAL].Count)];
        }

        /// <summary>
        /// Get a context-aware applying message based on optimization category
        /// </summary>
        public string GetApplyingMessage(string optimizationCategory)
        {
            var category = GetCategoryKey(optimizationCategory);
            
            if (_applyingMessages.TryGetValue(category, out var messages) && messages.Any())
            {
                return messages[_random.Next(messages.Count)];
            }
            
            return _applyingMessages[GENERAL][_random.Next(_applyingMessages[GENERAL].Count)];
        }

        /// <summary>
        /// Get a dynamic status message for a specific optimization step
        /// </summary>
        public string GetDynamicStatusMessage(OptimizationPhase phase, string optimizationName, string? category = null)
        {
            var verb = GetStatusVerb(phase);
            var context = category ?? ExtractCategoryFromName(optimizationName);
            
            return phase switch
            {
                OptimizationPhase.Analyzing => GetThinkingMessage(context),
                OptimizationPhase.Applying => GetApplyingMessage(context),
                OptimizationPhase.Processing => $"{verb} {optimizationName.ToLowerInvariant()} settings...",
                OptimizationPhase.Validating => $"{verb} {optimizationName.ToLowerInvariant()} results...",
                OptimizationPhase.Finalizing => $"{verb} {optimizationName.ToLowerInvariant()} implementation...",
                _ => $"{verb} {optimizationName.ToLowerInvariant()}..."
            };
        }

        /// <summary>
        /// Generate a completion message with personality
        /// </summary>
        public string GetCompletionMessage(int completedOptimizations)
        {
            var messages = new List<string>
            {
                $"🎉 Brilliant! Successfully applied {completedOptimizations} intelligent optimizations to your system!",
                $"✨ Fantastic! Your PC is now running {completedOptimizations} optimizations stronger!",
                $"🚀 Outstanding! {completedOptimizations} performance improvements have been implemented!",
                $"🏆 Excellent work! {completedOptimizations} optimizations completed with precision!",
                $"💫 Superb! Your system is now enhanced with {completedOptimizations} smart improvements!",
                $"🎯 Perfect! {completedOptimizations} targeted optimizations successfully deployed!",
                $"🎪 Amazing! {completedOptimizations} performance boosts have been orchestrated flawlessly!"
            };
            
            return messages[_random.Next(messages.Count)];
        }

        /// <summary>
        /// Extract category from optimization name for context-aware messaging
        /// </summary>
        private static string ExtractCategoryFromName(string optimizationName)
        {
            var name = optimizationName.ToLowerInvariant();
            
            if (name.Contains("startup") || name.Contains("boot")) return STARTUP;
            if (name.Contains("memory") || name.Contains("ram")) return MEMORY;
            if (name.Contains("disk") || name.Contains("storage") || name.Contains("ssd") || name.Contains("hdd")) return STORAGE;
            if (name.Contains("graphics") || name.Contains("gpu") || name.Contains("video")) return GRAPHICS;
            if (name.Contains("network") || name.Contains("internet") || name.Contains("connection")) return NETWORK;
            if (name.Contains("cpu") || name.Contains("processor") || name.Contains("power")) return CPU;
            
            return GENERAL;
        }

        /// <summary>
        /// Get the appropriate category key for messaging
        /// </summary>
        private static string GetCategoryKey(string category)
        {
            var key = category.ToLowerInvariant();
            return key switch
            {
                "performance" => CPU,
                "visual" => GRAPHICS,
                "animation" => GRAPHICS,
                _ => key
            };
        }
    }

    /// <summary>
    /// Represents different phases of optimization for dynamic status messaging
    /// </summary>
    public enum OptimizationPhase
    {
        Analyzing,
        Processing, 
        Applying,
        Validating,
        Finalizing
    }
}
