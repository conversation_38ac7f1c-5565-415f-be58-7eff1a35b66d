using System;
using System.Windows;
using System.Windows.Controls;
using PCOptimizerApp.ViewModels;

namespace PCOptimizerApp.Views
{
    /// <summary>
    /// Fast startup page for quick system information display
    /// </summary>
    public partial class FastStartupPage : Page
    {
        public event EventHandler<string>? NavigationRequested;

        private readonly MainWindowViewModel _mainWindowViewModel;

        public FastStartupPage(MainWindowViewModel mainWindowViewModel)
        {
            InitializeComponent();
            _mainWindowViewModel = mainWindowViewModel;
        }

        private void QuickScanButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "SmartAnalysis");
        }

        private void SmartAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "SmartAnalysis");
        }

        private void QuickOptimizeButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "ModernOptimization");
        }

        private void ViewDashboardButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Dashboard");
        }

        private void OptimizeButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "ModernOptimization");
        }

        private void AdvancedButton_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "OptimizationPlanning");
        }
    }
}