using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace PCOptimizerApp.Views
{
    public partial class LiveOptimizationPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<LiveOptimizationPage>();
        private readonly IOptimizationService _optimizationService;
        private readonly IProgressTrackingService _progressTrackingService;
        private readonly DispatcherTimer _metricsUpdateTimer;
        private readonly StatusMessageService _statusMessageService;

        private ObservableCollection<OptimizationProgressItem> _progressHistory = new();
        private readonly ObservableCollection<ChecklistItem> _checklistItems = new();
        private bool _isPaused = false;
        private int _totalOptimizations = 0;
        private int _completedOptimizations = 0;

        // Performance metrics tracking for simulation
        private readonly double _initialBootTime = 45.0;
        private readonly double _initialAppLoad = 3.2;
        private readonly double _initialMemoryEfficiency = 72.0;
        private readonly double _initialDiskPerformance = 450.0;

        private readonly SemaphoreSlim _textUpdateSemaphore = new(1, 1);

        public LiveOptimizationPage()
        {
            InitializeComponent();

            // Get services from DI container
            _optimizationService = ServiceLocator.GetService<IOptimizationService>();
            _progressTrackingService = ServiceLocator.GetService<IProgressTrackingService>();

            // Initialize status message service
            _statusMessageService = new StatusMessageService();

            // Set up data binding
            ProgressHistoryList.ItemsSource = _progressHistory;
            ChecklistItemsControl.ItemsSource = _checklistItems;

            // Subscribe to progress events
            _progressTrackingService.ProgressUpdated += OnProgressUpdated;

            // Set up metrics update timer
            _metricsUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _metricsUpdateTimer.Tick += UpdateMetrics;
            _metricsUpdateTimer.Start();

            // Initialize UI
            InitializeOptimizationDisplay();
        }

        private void InitializeOptimizationDisplay()
        {
            try
            {
                // Set initial state - ready to start
                CurrentOptimizationTitle.Text = "Ready to Optimize Your PC";
                OptimizationProgressCountText.Text = "Waiting to begin optimization...";
                CurrentOptimizationStatus.Text = "Click 'Start Optimization' to begin applying intelligent performance improvements to your system";
                // CurrentOptimizationIcon.Text = "[READY]"; // Removed icon display
                CurrentOptimizationProgress.Value = 0;

                // Initial state - no detailed text needed yet

                // Initialize metrics
                UpdatePerformanceMetrics();
                UpdateValueMetrics();

                _logger.Information("Live optimization display initialized");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error initializing optimization display");
            }
        }

        private async Task PerformIntelligentOptimization()
        {
            try
            {
                _logger.Information("Starting intelligent optimization process");

                // Get available optimizations from the service
                var availableOptimizations = await _optimizationService.GetAvailableOptimizationsAsync();
                var safeOptimizations = availableOptimizations
                    .Where(o => o.IsApplicable && o.Safety >= OptimizationSafety.MostlySafe)
                    .Take(8) // Limit to 8 optimizations for demo
                    .ToList();

                _totalOptimizations = safeOptimizations.Count;

                // Apply each optimization with detailed reasoning
                for (int i = 0; i < safeOptimizations.Count; i++)
                {
                    if (_isPaused)
                    {
                        // Wait while paused
                        while (_isPaused)
                        {
                            await Task.Delay(500);
                        }
                    }

                    var optimization = safeOptimizations[i];
                    var progressPercentage = (int)((double)(i + 1) / safeOptimizations.Count * 100);

                    // Show detailed thinking process
                    await ShowDetailedOptimizationProcess(optimization, i + 1, safeOptimizations.Count);

                    // Check if already optimized
                    if (optimization.IsApplied)
                    {
                        await ShowAlreadyOptimizedMessage(optimization);
                        continue;
                    }

                    // Actually apply the optimization
                    var success = await _optimizationService.ApplyOptimizationAsync(optimization.Id ?? "");

                    if (success)
                    {
                        _completedOptimizations++;
                        await ShowOptimizationSuccess(optimization);
                    }
                    else
                    {
                        await ShowOptimizationSkipped(optimization);
                    }

                    // Add realistic delay for user to read
                    await Task.Delay(Random.Shared.Next(3000, 5000));
                }

                // Show completion
                await ShowOptimizationComplete();

                _logger.Information("Intelligent optimization process completed. Applied {Count} optimizations", _completedOptimizations);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during intelligent optimization process");

                CurrentOptimizationTitle.Text = "Optimization Failed";
                CurrentOptimizationStatus.Text = $"ERROR: An error occurred: {ex.Message}";
                // CurrentOptimizationIcon.Text = "[ERROR]"; // Removed icon display
            }
        }

        private async Task ShowDetailedOptimizationProcess(OptimizationItem optimization, int currentStep, int totalSteps)
        {
            var progressPercentage = (int)((double)currentStep / totalSteps * 100);

            // Update main display and reset sections
            await Dispatcher.InvokeAsync(() =>
            {
                CurrentOptimizationTitle.Text = $"Step {currentStep}/{totalSteps}: {optimization.Name}";
                OptimizationProgressCountText.Text = $"Performing optimization task {currentStep} of {totalSteps}...";
                // CurrentOptimizationIcon.Text = GetOptimizationIcon(optimization.Category ?? ""); // Removed icon display
                CurrentOptimizationProgress.Value = progressPercentage;
                OverallProgressText.Text = $"{progressPercentage}%";

                // Reset all sections to collapsed
                ExpertAnalysisSection.Visibility = Visibility.Collapsed;
                TechnicalDetailsSection.Visibility = Visibility.Collapsed;
                ExpectedImpactSection.Visibility = Visibility.Collapsed;

                // Clear previous text
                ExpertAnalysisText.Text = "";
                TechnicalDetailsText.Text = "";
                ExpectedImpactText.Text = "";

                // CRITICAL: Ensure user can see the current step without scrolling
                EnsureCurrentOptimizationVisible();
            });

            // Show detailed thinking process with expert content and dynamic status messages
            var expertContent = GetDetailedOptimizationContent(optimization);

            // Populate the checklist
            await Dispatcher.InvokeAsync(() =>
            {
                _checklistItems.Clear();
                foreach (var item in expertContent.ChecklistItems)
                {
                    _checklistItems.Add(new ChecklistItem
                    {
                        Description = item,
                        Category = optimization.Category?.ToUpperInvariant() ?? "SYSTEM",
                        StatusIcon = "[ ]",
                        StatusText = "Pending",
                        IsCompleted = false
                    });
                }

                // Initialize checklist progress
                UpdateChecklistProgress();
            });

            await ShowReasoningStepsWithDynamicStatus(expertContent, optimization);
        }

        private async Task ShowAlreadyOptimizedMessage(OptimizationItem optimization)
        {
            Dispatcher.Invoke(() =>
            {
                CurrentOptimizationStatus.Text = $"COMPLETE: {optimization.Name} is already at optimal settings. No changes needed.";

                _progressHistory.Insert(0, new OptimizationProgressItem
                {
                    Name = optimization.Name ?? "Optimization",
                    Status = "COMPLETE: Already Optimal",
                    Impact = "No changes needed - already optimized",
                    Time = DateTime.Now.ToString("HH:mm:ss"),
                    StatusIcon = "OK"
                });
            });

            await Task.Delay(2000); // Give user time to read
        }

        private async Task ShowOptimizationSuccess(OptimizationItem optimization)
        {
            await Dispatcher.InvokeAsync(() =>
            {
                CurrentOptimizationStatus.Text = $"SUCCESS: Applied {optimization.Name}";

                _progressHistory.Insert(0, new OptimizationProgressItem
                {
                    Name = optimization.Name ?? "Optimization",
                    Status = "SUCCESS: Applied Successfully",
                    Impact = optimization.ExpectedImprovement ?? "Performance improved",
                    Time = DateTime.Now.ToString("HH:mm:ss"),
                    StatusIcon = "OK"
                });
            });
        }

        private async Task ShowOptimizationSkipped(OptimizationItem optimization)
        {
            await Dispatcher.InvokeAsync(() =>
            {
                CurrentOptimizationStatus.Text = $"SKIPPED: {optimization.Name} - Current settings are already optimal or incompatible";

                _progressHistory.Insert(0, new OptimizationProgressItem
                {
                    Name = optimization.Name ?? "Optimization",
                    Status = "SKIPPED",
                    Impact = "No changes made - settings already optimal",
                    Time = DateTime.Now.ToString("HH:mm:ss"),
                    StatusIcon = "SKIP"
                });
            });
        }

        private async Task ShowOptimizationComplete()
        {
            await Dispatcher.InvokeAsync(() =>
            {
                CurrentOptimizationTitle.Text = "Optimization Complete!";
                OptimizationProgressCountText.Text = $"Finished all {_totalOptimizations} optimization tasks!";
                CurrentOptimizationStatus.Text = $"SUCCESS: PC optimization complete! Applied {_completedOptimizations} improvements.";
                // CurrentOptimizationIcon.Text = "[DONE]"; // Removed icon display
                CurrentOptimizationProgress.Value = 100;
                OverallProgressText.Text = "100%";

                // Hide pause button, show start button for next run
                PauseResumeButton.Visibility = Visibility.Collapsed;
                StartOptimizationButton.Visibility = Visibility.Visible;
                StartOptimizationButton.Content = "Run Again";
            });
        }

        // Removed GetOptimizationIcon method - no longer needed since icon display was removed

        private static OptimizationExpertContent GetDetailedOptimizationContent(OptimizationItem optimization)
        {
            var name = optimization.Name?.ToLower() ?? "";

            if (name.Contains("animation") || name.Contains("visual"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Most PCs face slow apparent speed because of unnecessary animations. I will check for various animation settings and come up with a golden medium where your PC feels snappy while maintaining visual appeal.",
                    ExpertAnalysis = "Windows animation systems consume CPU and GPU resources. Reducing animation duration by 50% can improve responsiveness by up to 40%. Analyzing window transitions, menu animations, taskbar effects, and desktop composition.",
                    TechnicalDetails = "Modifying registry keys: HKEY_CURRENT_USER\\Control Panel\\Desktop\\MenuShowDelay, UserPreferencesMask, and DWM composition settings. Adjusting SystemParametersInfo calls for SPI_SETMENUFADE, SPI_SETMENUANIMATION. Optimizing GPU scheduling for desktop window manager while maintaining Aero Glass effects where beneficial.",
                    ExpectedImpact = "• 25-40% faster window opening/closing\n• 15-30% improvement in menu responsiveness\n• Reduced CPU usage during UI interactions\n• Smoother multitasking experience\n• Maintained visual appeal with optimized timing",
                    ChecklistItems = new List<string>
                    {
                        "Analyze current animation settings",
                        "Reduce window animation duration",
                        "Optimize menu fade-in/out speed",
                        "Disable unnecessary taskbar animations",
                        "Set visual effects to 'Adjust for best performance'"
                    }
                };
            }
            else if (name.Contains("startup") || name.Contains("boot"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Your PC loads many programs at startup that you rarely use. I'm analyzing which programs are essential and which can be delayed or disabled to dramatically improve boot times.",
                    ExpertAnalysis = "Startup optimization is highly impactful for performance. Scanning Registry Run keys, Startup folder, Services, and Task Scheduler. Classifying programs by importance: Critical, Important, Optional, and Unnecessary.",
                    TechnicalDetails = "Analyzing HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run, HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run, shell:startup folder, and msconfig entries. Implementing delayed startup for non-critical applications using Task Scheduler with optimized triggers. Configuring service startup types: Automatic (Delayed Start) for non-essential services.",
                    ExpectedImpact = "• 40-70% faster boot times\n• Reduced memory usage at startup\n• Faster desktop responsiveness\n• Lower CPU usage during boot\n• Preserved functionality of important programs",
                    ChecklistItems = new List<string>
                    {
                        "Scan Registry for startup programs",
                        "Analyze Startup folder applications",
                        "Review non-essential Windows services",
                        "Identify bloatware and redundant updaters",
                        "Disable low-impact startup items",
                        "Set non-critical services to 'Delayed Start'"
                    }
                };
            }
            else if (name.Contains("memory") || name.Contains("ram"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Memory management is crucial for smooth multitasking. I'm optimizing how Windows allocates and manages RAM to prevent slowdowns when running multiple applications.",
                    ExpertAnalysis = "Windows memory management involves virtual memory, page files, and RAM allocation. Analyzing memory usage patterns, identifying leaks, and optimizing settings for better performance and reduced fragmentation.",
                    TechnicalDetails = "Configuring virtual memory with optimal page file size (1.5x RAM for systems <16GB, fixed size for >16GB). Enabling memory compression, optimizing working set trimming, and configuring prefetch/superfetch settings. Adjusting LargeSystemCache, DisablePagingExecutive, and ClearPageFileAtShutdown registry values based on your system specifications.",
                    ExpectedImpact = "• 20-35% better multitasking performance\n• Reduced application loading times\n• Less frequent disk thrashing\n• Improved system responsiveness under load\n• Better memory utilization efficiency",
                    ChecklistItems = new List<string>
                    {
                        "Analyze current memory usage patterns",
                        "Optimize virtual memory page file size",
                        "Enable intelligent memory compression",
                        "Adjust system cache settings for your hardware",
                        "Configure prefetch/superfetch for faster app loading"
                    }
                };
            }
            else if (name.Contains("disk") || name.Contains("storage") || name.Contains("ssd"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Storage optimization can provide massive performance gains. I'm configuring advanced disk settings, enabling TRIM for SSDs, and optimizing file system performance.",
                    ExpertAnalysis = "Storage is often the biggest performance bottleneck. For SSDs, optimizing TRIM commands maintains write performance. Optimizing NTFS settings, cluster sizes, and disk caching for maximum efficiency.",
                    TechnicalDetails = "Enabling TRIM via 'fsutil behavior set DisableDeleteNotify 0', optimizing NTFS with 'fsutil behavior set EncryptPagingFile 0'. Configuring disk write caching, adjusting queue depth settings, and optimizing Windows Search indexing. Setting optimal cluster sizes based on usage patterns and enabling compression for appropriate file types.",
                    ExpectedImpact = "• 30-60% faster file operations\n• Extended SSD lifespan (20-40% longer)\n• Reduced boot and application load times\n• Better sustained write performance\n• Optimized storage space utilization",
                    ChecklistItems = new List<string>
                    {
                        "Verify SSD TRIM is enabled and active",
                        "Optimize NTFS file system for performance",
                        "Configure disk write caching for your drive",
                        "Adjust Windows Search indexing priorities",
                        "Disable unnecessary disk-intensive services"
                    }
                };
            }
            else if (name.Contains("graphics") || name.Contains("gpu"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Graphics settings often prioritize quality over performance. I'm finding the optimal balance between visual quality and frame rates for your specific hardware.",
                    ExpertAnalysis = "GPU optimization involves driver settings, Windows graphics preferences, and application configs. Analyzing GPU capabilities, VRAM usage, and optimizing power management for your workload.",
                    TechnicalDetails = "Configuring GPU scheduling mode, optimizing DirectX and OpenGL settings, and adjusting hardware-accelerated GPU scheduling. Setting optimal power management modes, configuring multi-display setups for efficiency, and optimizing VRAM allocation. Adjusting Windows Graphics Settings for per-application GPU preferences.",
                    ExpectedImpact = "• 15-35% improvement in gaming performance\n• Better video playback efficiency\n• Reduced GPU power consumption\n• Smoother desktop composition\n• Optimized multi-monitor performance",
                    ChecklistItems = new List<string>
                    {
                        "Enable Hardware-accelerated GPU Scheduling",
                        "Set power management to 'Prefer maximum performance'",
                        "Optimize graphics settings for individual applications",
                        "Adjust DirectX and OpenGL configurations",
                        "Fine-tune VRAM allocation for stability"
                    }
                };
            }
            else if (name.Contains("network") || name.Contains("internet"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "Network optimization can significantly improve browsing and download speeds. I'm adjusting TCP/IP settings and network adapter configurations for maximum throughput.",
                    ExpertAnalysis = "Network performance depends on TCP/IP stack configuration, DNS optimization, and adapter settings. Optimizing receive window scaling, congestion control, and MTU sizing for your connection.",
                    TechnicalDetails = "Optimizing TCP window scaling, adjusting receive side scaling (RSS), and configuring network adapter advanced properties. Setting optimal DNS servers (Cloudflare *******, Google *******), enabling TCP Chimney Offload where supported, and optimizing network throttling index. Configuring QoS policies for prioritized traffic.",
                    ExpectedImpact = "• 20-50% faster download speeds\n• Reduced web page loading times\n• Lower network latency\n• Better streaming performance\n• Improved online gaming experience",
                    ChecklistItems = new List<string>
                    {
                        "Optimize TCP/IP settings for faster throughput",
                        "Set faster DNS servers (e.g., Cloudflare, Google)",
                        "Adjust network adapter properties for performance",
                        "Disable network throttling policies",
                        "Prioritize critical applications with QoS"
                    }
                };
            }
            else if (name.Contains("power") || name.Contains("cpu"))
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "CPU power management affects both performance and battery life. I'm configuring processor settings to deliver maximum performance when needed while maintaining efficiency.",
                    ExpertAnalysis = "Modern CPUs use complex power management with P-states and C-states. Analyzing CPU capabilities, thermal characteristics, and balancing maximum performance with thermal efficiency.",
                    TechnicalDetails = "Configuring Windows power plans with custom processor power management settings. Adjusting minimum and maximum processor states, core parking policies, and frequency scaling algorithms. Optimizing CPU scheduling, interrupt handling, and thermal management. Setting appropriate C-state and P-state configurations for your specific CPU model.",
                    ExpectedImpact = "• 10-25% better CPU performance\n• Improved thermal management\n• Better battery life (laptops)\n• Reduced CPU throttling\n• Optimized multi-core utilization",
                    ChecklistItems = new List<string>
                    {
                        "Set power plan to 'Ultimate Performance'",
                        "Adjust processor power management settings",
                        "Disable CPU core parking for faster response",
                        "Optimize CPU scheduling for foreground apps",
                        "Fine-tune thermal management policies"
                    }
                };
            }
            else
            {
                return new OptimizationExpertContent
                {
                    MainReasoning = "This optimization addresses a specific performance bottleneck in your system. I'm carefully analyzing the current configuration and applying proven improvements.",
                    ExpertAnalysis = "Using advanced diagnostic techniques to identify bottlenecks based on your hardware configuration and software patterns. Applying targeted optimizations using performance counters and system analysis.",
                    TechnicalDetails = "Performing comprehensive system analysis using Windows Performance Toolkit, analyzing ETW traces, and examining system configuration. Applying evidence-based optimizations from performance research and real-world testing. Implementing changes with proper backup and rollback capabilities.",
                    ExpectedImpact = "• Targeted performance improvements\n• Reduced system bottlenecks\n• Better resource utilization\n• Enhanced system stability\n• Measurable performance gains",
                    ChecklistItems = new List<string>
                    {
                        "Perform deep system analysis",
                        "Identify primary performance bottleneck",
                        "Apply targeted registry/policy change",
                        "Verify improvement with performance counters",
                        "Ensure system stability after change"
                    }
                };
            }
        }

        private async Task ShowReasoningStepsWithDynamicStatus(OptimizationExpertContent content, OptimizationItem optimization)
        {
            try
            {
                // Step 1: Dynamic thinking phase - Type text with animation
                var thinkingMessages = GetThinkingMessagesForCategory(optimization.Category ?? "");
                await TypeTextAndWait(CurrentOptimizationStatus, thinkingMessages[Random.Shared.Next(thinkingMessages.Count)], 12);
                await TickChecklistItem(0);

                // Step 2: Main reasoning - Type text with animation
                await TypeTextAndWait(CurrentOptimizationStatus, content.MainReasoning, 12);
                await TickChecklistItem(1);

                // Step 3: Show expert analysis section and type text
                await DispatcherInvokeAsync(() =>
                {
                    ExpertAnalysisSection.Visibility = Visibility.Visible;
                });
                var formattedAnalysis = FormatTextWithLineBreaks(content.ExpertAnalysis);
                await TypeTextAndWait(ExpertAnalysisText, formattedAnalysis, 8);
                await TickChecklistItem(2);

                // Step 4: Show technical details - Type formatted text
                await DispatcherInvokeAsync(() =>
                {
                    TechnicalDetailsSection.Visibility = Visibility.Visible;
                });
                var formattedTechnical = FormatTextWithLineBreaks(content.TechnicalDetails);
                await TypeTextAndWait(TechnicalDetailsText, formattedTechnical, 8);
                await TickChecklistItem(3);

                // Step 5: Show expected impact - Type text
                await DispatcherInvokeAsync(() =>
                {
                    ExpectedImpactSection.Visibility = Visibility.Visible;
                });
                await TypeTextAndWait(ExpectedImpactText, content.ExpectedImpact, 10);

                // Step 6: Final application status - Type text
                var applyingMessages = GetApplyingMessagesForCategory(optimization.Category ?? "");
                await TypeTextAndWait(CurrentOptimizationStatus, applyingMessages[Random.Shared.Next(applyingMessages.Count)], 12);

                // Complete remaining checklist items with smooth progression
                await ProgressThroughChecklistWithAnimations(content.ChecklistItems);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error showing reasoning steps");
                await SetTextSafely(CurrentOptimizationStatus, "An error occurred while displaying optimization details.");
            }
        }

        private async Task SetTextSafely(TextBlock textBlock, string text)
        {
            try
            {
                await _textUpdateSemaphore.WaitAsync();
                try
                {
                    await Dispatcher.InvokeAsync(() =>
                    {
                        textBlock.Text = text ?? string.Empty;
                        EnsureCurrentOptimizationVisible();
                    });
                }
                finally
                {
                    _textUpdateSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error safely setting text for {TextBlockName}", textBlock.Name);
            }
        }

        private async Task DispatcherInvokeAsync(Action action)
        {
            try
            {
                await Dispatcher.InvokeAsync(action);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error in dispatcher invoke");
            }
        }

        private void EnsureCurrentOptimizationVisible()
        {
            try
            {
                // Find the main page ScrollViewer and scroll to show the current optimization card
                if (Parent is ScrollViewer mainScrollViewer)
                {
                    mainScrollViewer.ScrollToTop();
                }
                else
                {
                    // Try to find a parent ScrollViewer in the visual tree
                    var parent = this.Parent;
                    while (parent != null)
                    {
                        if (parent is ScrollViewer scrollViewer)
                        {
                            scrollViewer.ScrollToTop();
                            break;
                        }
                        parent = parent is FrameworkElement fe ? fe.Parent : null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error ensuring current optimization visibility");
            }
        }

        private async Task TickChecklistItem(int index)
        {
            await Dispatcher.InvokeAsync(() =>
            {
                if (index >= 0 && index < _checklistItems.Count)
                {
                    // Mark as completed with animation
                    _checklistItems[index].IsCompleted = true;
                    _checklistItems[index].StatusIcon = "[✓]";
                    _checklistItems[index].StatusText = "Completed";

                    // Update progress tracking
                    UpdateChecklistProgress();

                    // For smooth visual feedback, add a small delay
                    Task.Delay(100).ContinueWith(_ =>
                    {
                        Dispatcher.InvokeAsync(() =>
                        {
                            // Force UI update to trigger animations
                            ChecklistItemsControl.InvalidateVisual();
                        });
                    });
                }
            });
        }

        private async Task ProgressThroughChecklistWithAnimations(List<string> checklistItems)
        {
            // Progress through each checklist item with smooth animations
            for (int i = 0; i < checklistItems.Count && i < _checklistItems.Count; i++)
            {
                await TickChecklistItem(i);

                // Slower progression for better visual feedback and readability
                await Task.Delay(800); // Increased from previous timing for better UX
            }
        }

        // Dynamic status message helper methods
        private static List<string> GetThinkingMessagesForCategory(string category)
        {
            var cat = category.ToLowerInvariant();
            return cat switch
            {
                "startup" => new List<string>
                {
                    "ANALYZING: Evaluating which programs really need to start with Windows...",
                    "THINKING: Considering startup impact on boot performance...",
                    "SCANNING: Analyzing startup dependencies and priorities..."
                },
                "memory" => new List<string>
                {
                    "ANALYZING: Evaluating optimal memory allocation strategies...",
                    "THINKING: Considering virtual memory and paging file settings...",
                    "SCANNING: Analyzing memory usage patterns and bottlenecks..."
                },
                "storage" => new List<string>
                {
                    "ANALYZING: Evaluating disk performance optimization techniques...",
                    "THINKING: Considering SSD vs HDD specific optimizations...",
                    "SCANNING: Analyzing file system efficiency and fragmentation..."
                },
                "graphics" => new List<string>
                {
                    "ANALYZING: Evaluating GPU performance vs. visual quality balance...",
                    "THINKING: Considering graphics driver and hardware acceleration settings...",
                    "SCANNING: Analyzing display and rendering optimization opportunities..."
                },
                _ => new List<string>
                {
                    "ANALYZING: Evaluating this optimization carefully...",
                    "THINKING: Considering the best approach for your system...",
                    "SCANNING: Analyzing configuration options and impacts..."
                }
            };
        }

        private static List<string> GetApplyingMessagesForCategory(string category)
        {
            var cat = category.ToLowerInvariant();
            return cat switch
            {
                "startup" => new List<string>
                {
                    "APPLYING: Implementing intelligent startup management - optimizing your boot process...",
                    "EXECUTING: Deploying startup optimizations - your PC will boot faster...",
                    "CONFIGURING: Applying startup improvements - faster boots incoming..."
                },
                "memory" => new List<string>
                {
                    "APPLYING: Implementing advanced memory management - optimizing RAM utilization...",
                    "EXECUTING: Deploying memory efficiency protocols - better multitasking ahead...",
                    "CONFIGURING: Applying RAM optimizations - smoother performance loading..."
                },
                "storage" => new List<string>
                {
                    "APPLYING: Implementing storage performance enhancements - maximizing disk efficiency...",
                    "EXECUTING: Deploying advanced disk optimizations - faster file operations coming...",
                    "CONFIGURING: Applying SSD/HDD performance tuning - optimizing for your hardware..."
                },
                _ => new List<string>
                {
                    "APPLYING: Implementing this optimization with precision...",
                    "EXECUTING: Deploying performance improvements intelligently...",
                    "CONFIGURING: Applying system enhancements strategically..."
                }
            };
        }

        /// <summary>
        /// Gets an enhanced title for the current optimization step
        /// </summary>
        private static string GetEnhancedTitle(string stepDescription)
        {
            return stepDescription switch
            {
                var s when s.Contains("SSD") => "SSD Performance Optimization",
                var s when s.Contains("Memory") => "Memory Management Enhancement",
                var s when s.Contains("Startup") => "Startup Performance Boost",
                var s when s.Contains("Registry") => "Registry Optimization",
                var s when s.Contains("Cache") => "System Cache Optimization",
                var s when s.Contains("Graphics") => "Graphics Performance Tuning",
                var s when s.Contains("Network") => "Network Performance Enhancement",
                var s when s.Contains("Power") => "Power Management Optimization",
                _ => stepDescription
            };
        }

        /// <summary>
        /// Gets an enhanced status message with dynamic verbs
        /// </summary>
        private string GetEnhancedStatusMessage(string details, string stepDescription)
        {
            var verb = StatusMessageService.GetDynamicStatusVerb(stepDescription);
            return $"{verb} {details}";
        }

        /// <summary>
        /// Gets the expected impact description for a specific optimization step
        /// </summary>
        private static string GetExpectedImpactForStep(string stepDescription)
        {
            return stepDescription switch
            {
                var s when s.Contains("SSD") => "Faster file operations and reduced load times",
                var s when s.Contains("Memory") => "Improved RAM efficiency and multitasking",
                var s when s.Contains("Startup") => "Faster boot time and application launch",
                var s when s.Contains("Registry") => "Enhanced system responsiveness",
                var s when s.Contains("Cache") => "Optimized data access patterns",
                var s when s.Contains("Graphics") => "Smoother visual performance",
                var s when s.Contains("Network") => "Improved connectivity and download speeds",
                var s when s.Contains("Power") => "Better battery life and thermal management",
                _ => "General system performance improvement"
            };
        }
        // END OF METHODS, START OF EVENT HANDLERS AND UI LOGIC

        private void OnProgressUpdated(object? sender, ProgressUpdateEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    // Update current optimization display with dynamic status messages
                    var dynamicTitle = GetEnhancedTitle(e.CurrentStepDescription);
                    var dynamicStatus = GetEnhancedStatusMessage(e.Details ?? "Processing...", e.CurrentStepDescription);

                    CurrentOptimizationTitle.Text = dynamicTitle;
                    CurrentOptimizationStatus.Text = dynamicStatus;
                    CurrentOptimizationProgress.Value = e.ProgressPercentage;
                    OverallProgressText.Text = $"{e.ProgressPercentage:F0}%";

                    // CRITICAL: Ensure the current status is always visible to the user
                    EnsureCurrentOptimizationVisible();

                    // Add to progress history
                    var progressItem = new OptimizationProgressItem
                    {
                        Name = e.CurrentStepDescription,
                        Description = dynamicStatus,
                        Impact = GetExpectedImpactForStep(e.CurrentStepDescription),
                        StatusIcon = e.ProgressPercentage >= 100 ? "DONE" : "WORK",
                        Timestamp = DateTime.Now
                    };

                    _progressHistory.Insert(0, progressItem);

                    // Keep only last 10 items (reduced from 20 to minimize scrolling)
                    while (_progressHistory.Count > 10)
                    {
                        _progressHistory.RemoveAt(_progressHistory.Count - 1);
                    }

                    // Don't auto-scroll progress history - let users manually scroll if they want to see history
                    // ProgressHistoryScroll.ScrollToTop(); // Removed this line

                    _logger.Debug("Progress updated with dynamic messaging: {Step} - {Progress}%", e.CurrentStepDescription, e.ProgressPercentage);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error updating progress UI");
                }
            });
        }

        private void UpdateMetrics(object? sender, EventArgs e)
        {
            try
            {
                UpdatePerformanceMetrics();
                UpdateValueMetrics();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating metrics");
            }
        }

        private void UpdatePerformanceMetrics()
        {
            // Simulate performance improvements based on completed optimizations
            var improvementFactor = Math.Min(_completedOptimizations * 0.05, 0.3); // Max 30% improvement

            var currentBootTime = _initialBootTime * (1 - improvementFactor);
            var currentAppLoad = _initialAppLoad * (1 - improvementFactor);
            var currentMemoryEfficiency = _initialMemoryEfficiency * (1 + improvementFactor * 0.5);
            var currentDiskPerformance = _initialDiskPerformance * (1 + improvementFactor);

            var bootImprovement = ((_initialBootTime - currentBootTime) / _initialBootTime) * 100;
            var appImprovement = ((_initialAppLoad - currentAppLoad) / _initialAppLoad) * 100;
            var memoryImprovement = ((currentMemoryEfficiency - _initialMemoryEfficiency) / _initialMemoryEfficiency) * 100;
            var diskImprovement = ((currentDiskPerformance - _initialDiskPerformance) / _initialDiskPerformance) * 100;

            BootTimeChange.Text = $"+{bootImprovement:F0}%";
            AppLoadChange.Text = $"+{appImprovement:F0}%";
            MemoryEfficiencyChange.Text = $"+{memoryImprovement:F0}%";
            DiskPerformanceChange.Text = $"+{diskImprovement:F0}%";

            BootTimeValue.Text = $"{currentBootTime:F0}s";
            AppLoadValue.Text = $"{currentAppLoad:F1}s";
            MemoryEfficiencyValue.Text = $"{currentMemoryEfficiency:F0}%";
            DiskPerformanceValue.Text = $"{currentDiskPerformance:F0}MB/s";
        }

        private void UpdateValueMetrics()
        {
            var moneyValue = _completedOptimizations * 15; // $15 per optimization
            var timeSaved = _completedOptimizations * 2.5; // 2.5 minutes per week per optimization

            MoneyValueText.Text = $"${moneyValue}";
            TimeSavedText.Text = $"{timeSaved:F0} min";
            OptimizationsAppliedText.Text = _completedOptimizations.ToString();
        }

        private async void StartOptimizationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Information("User started live optimization");

                // Hide start button, show pause button
                StartOptimizationButton.Visibility = Visibility.Collapsed;
                PauseResumeButton.Visibility = Visibility.Visible;

                // Start the optimization process
                await PerformIntelligentOptimization();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error starting live optimization");

                // Reset UI on error
                StartOptimizationButton.Visibility = Visibility.Visible;
                PauseResumeButton.Visibility = Visibility.Collapsed;

                CurrentOptimizationTitle.Text = "Optimization Failed";
                CurrentOptimizationStatus.Text = $"ERROR: An error occurred: {ex.Message}";
                // CurrentOptimizationIcon.Text = "[ERROR]"; // Removed icon display
            }
        }

        private void PauseResumeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isPaused = !_isPaused;

                if (_isPaused)
                {
                    PauseResumeButton.Content = "Resume";
                    CurrentOptimizationStatus.Text = "PAUSED: Optimization paused by user - waiting for your command...";
                    _metricsUpdateTimer.Stop();
                    _logger.Information("Optimization paused by user");
                }
                else
                {
                    PauseResumeButton.Content = "Pause";
                    CurrentOptimizationStatus.Text = "RESUMING: Getting back to work...";
                    _metricsUpdateTimer.Start();
                    _logger.Information("Optimization resumed by user");
                }

                // Ensure the status change is visible to the user
                EnsureCurrentOptimizationVisible();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error toggling pause/resume");
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Cleanup
                _progressTrackingService.ProgressUpdated -= OnProgressUpdated;
                _metricsUpdateTimer?.Stop();
                _logger.Debug("Live optimization page unloaded");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during page unload");
            }
        }

        /// <summary>
        /// Updates the checklist progress bar and summary text
        /// </summary>
        private void UpdateChecklistProgress()
        {
            try
            {
                var completedCount = _checklistItems.Count(x => x.IsCompleted);
                var totalCount = _checklistItems.Count;

                if (totalCount > 0)
                {
                    var progressPercentage = (double)completedCount / totalCount * 100;

                    // Update progress bar
                    ChecklistProgressBar.Value = progressPercentage;

                    // Update progress text
                    if (completedCount == 0)
                    {
                        ChecklistProgressText.Text = "Ready to start optimization";
                    }
                    else if (completedCount < totalCount)
                    {
                        ChecklistProgressText.Text = $"Optimization in progress... ({completedCount}/{totalCount})";
                    }
                    else
                    {
                        ChecklistProgressText.Text = "All optimizations completed!";
                    }

                    // Update summary text
                    ChecklistSummaryText.Text = $"{completedCount} of {totalCount} actions completed";
                }
                else
                {
                    ChecklistProgressBar.Value = 0;
                    ChecklistProgressText.Text = "Preparing optimization checklist...";
                    ChecklistSummaryText.Text = "0 of 0 actions completed";
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating checklist progress");
            }
        }

        /// <summary>
        /// Marks a checklist item as completed and updates the UI
        /// </summary>
        private void MarkChecklistItemCompleted(string description)
        {
            try
            {
                var item = _checklistItems.FirstOrDefault(x => x.Description == description);
                if (item != null)
                {
                    item.IsCompleted = true;
                    item.StatusIcon = "[✓]";
                    item.StatusText = "Completed";

                    _logger.Debug("Marked checklist item as completed: {Description}", description);

                    // Update progress
                    UpdateChecklistProgress();
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error marking checklist item as completed: {Description}", description);
            }
        }

        /// <summary>
        /// Formats text with improved word breaking and readability
        /// </summary>
        private static string FormatTextWithLineBreaks(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Create better formatted text with strategic line breaks
            return text
                .Replace(". ", ".\n")        // Single line break after sentences
                .Replace(": ", ":\n")        // Line breaks after colons
                .Replace(", HKEY_", ",\nHKEY_")  // Break long registry paths
                .Replace(", and ", ", and\n")    // Keep "and" with preceding text
                .Replace("settings. ", "settings.\n")  // Break after settings
                .Replace("calls for ", "calls for\n")    // Break technical details
                .Replace(". I'm ", ".\nI'm ")    // Break before "I'm" statements
                .Replace(". Using ", ".\nUsing ")  // Break before "Using" statements
                .Replace(". Implementing ", ".\nImplementing ")  // Break before "Implementing"
                .Replace(". Configuring ", ".\nConfiguring ")    // Break before "Configuring"
                .Replace("C-states", "C‑states")  // Use non-breaking hyphen to prevent word wrapping
                .Replace("T-states", "T‑states")  // Use non-breaking hyphen
                .Replace("P-states", "P‑states")  // Use non-breaking hyphen
                .Replace("multi-core", "multi‑core")  // Use non-breaking hyphen
                .Replace("real-time", "real‑time")  // Use non-breaking hyphen
                .Trim();
        }

        /// <summary>
        /// Fast typing animation that's readable but shows progress
        /// </summary>
        private async Task TypeTextFast(TextBlock textBlock, string text, int delayMs = 15)
        {
            await _textUpdateSemaphore.WaitAsync();
            try
            {
                if (string.IsNullOrEmpty(text))
                {
                    await Dispatcher.InvokeAsync(() => textBlock.Text = "");
                    return;
                }

                await Dispatcher.InvokeAsync(() => textBlock.Text = "");

                // Type text character by character with fast speed
                for (int i = 0; i <= text.Length; i++)
                {
                    var currentText = text[..i];
                    await Dispatcher.InvokeAsync(() =>
                    {
                        textBlock.Text = currentText;
                        EnsureCurrentOptimizationVisible();
                    });

                    if (i < text.Length)
                        await Task.Delay(delayMs);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error in fast typing animation for {TextBlockName}", textBlock.Name);
                // Fallback to direct text setting
                await Dispatcher.InvokeAsync(() => textBlock.Text = text ?? string.Empty);
            }
            finally
            {
                _textUpdateSemaphore.Release();
            }
        }

        /// <summary>
        /// Waits for typing animation to complete before proceeding
        /// </summary>
        private async Task TypeTextAndWait(TextBlock textBlock, string text, int delayMs = 15)
        {
            await TypeTextFast(textBlock, text, delayMs);
            // Add a small pause for readability after typing completes
            await Task.Delay(300);
        }
    }

    public class OptimizationProgressItem
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string StatusIcon { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class OptimizationExpertContent
    {
        public string MainReasoning { get; set; } = string.Empty;
        public string ExpertAnalysis { get; set; } = string.Empty;
        public string TechnicalDetails { get; set; } = string.Empty;
        public string ExpectedImpact { get; set; } = string.Empty;
        public List<string> ChecklistItems { get; set; } = new();
    }

    public class StatusMessageService
    {
        private readonly ILogger _logger = Log.ForContext<StatusMessageService>();

        public string GetStatusMessage(OptimizationItem optimization)
        {
            try
            {
                // Basic status messages
                if (optimization.IsApplied)
                    return $"✅ {optimization.Name} is already optimized.";
                else
                    return $"🔄 Applying {optimization.Name}...";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating status message");
                return "❌ Error generating status message";
            }
        }

        public string GetDetailedStatusMessage(OptimizationItem optimization)
        {
            try
            {
                // Detailed status messages with dynamic verbs
                var verb = optimization.IsApplied ? "Applied" : "Applying";
                var impact = "Expected to improve performance";

                return $"🚀 {verb} {optimization.Name}: {impact}";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating detailed status message");
                return "❌ Error generating detailed status message";
            }
        }

        /// <summary>
        /// Gets a dynamic status verb based on the optimization step description
        /// </summary>
        public static string GetDynamicStatusVerb(string stepDescription)
        {
            return stepDescription switch
            {
                var s when s.Contains("SSD") => "Optimizing",
                var s when s.Contains("Memory") => "Tuning",
                var s when s.Contains("Startup") => "Configuring",
                var s when s.Contains("Registry") => "Cleaning",
                var s when s.Contains("Cache") => "Flushing",
                var s when s.Contains("Graphics") => "Calibrating",
                var s when s.Contains("Network") => "Boosting",
                var s when s.Contains("Power") => "Managing",
                _ => "Applying"
            };
        }
    }
}
